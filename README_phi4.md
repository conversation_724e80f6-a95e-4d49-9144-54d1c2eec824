# Phi-4 本地模型运行器

这个项目提供了在本地运行 Phi-4 GGUF 量化模型的完整解决方案，专门配置为使用 GPU 1。

## 文件说明

- `phi4_local_runner.py` - 核心模型运行器类
- `run_phi4_gpu1.py` - 专门使用GPU 1的启动脚本
- `install_phi4_dependencies.sh` - 依赖安装脚本
- `test_phi4_model.py` - 模型测试脚本

## 安装步骤

### 1. 安装依赖
```bash
chmod +x install_phi4_dependencies.sh
./install_phi4_dependencies.sh
```

### 2. 准备模型文件
将你的 `phi-4-Q4_K_M.gguf` 模型文件放在以下位置之一：
- 当前目录
- `./models/` 目录
- `~/models/` 目录

## 使用方法

### 基本用法

#### 1. 交互模式
```bash
python3 run_phi4_gpu1.py --interactive
```

#### 2. 情感分析
```bash
python3 run_phi4_gpu1.py --text "I love this beautiful day!"
```

#### 3. 文本生成
```bash
python3 run_phi4_gpu1.py --prompt "Write a story about artificial intelligence"
```

#### 4. 批量处理
```bash
# 创建包含文本的文件
echo -e "I love this day!\nThis is terrible.\nIt's okay." > texts.txt

# 批量处理
python3 run_phi4_gpu1.py --batch-file texts.txt
```

### 高级选项

```bash
# 指定模型路径
python3 run_phi4_gpu1.py --model /path/to/phi-4-Q4_K_M.gguf --interactive

# 调整GPU层数（更多层=更快，但需要更多显存）
python3 run_phi4_gpu1.py --gpu-layers 40 --text "Hello world"

# 调整生成参数
python3 run_phi4_gpu1.py --prompt "Tell me a joke" --max-tokens 50 --temperature 0.9

# 启用详细日志
python3 run_phi4_gpu1.py --verbose --interactive
```

## GPU 配置

### 默认设置
- 使用 GPU 1 (`CUDA_VISIBLE_DEVICES=1`)
- 32层在GPU上运行（推荐值）
- 其余层在CPU上运行

### 显存要求
- Phi-4 Q4_K_M 量化模型大约需要 8-10GB 显存
- 如果显存不足，可以减少 `--gpu-layers` 参数

### 检查GPU状态
```bash
# 查看GPU使用情况
nvidia-smi

# 查看CUDA设备
python3 -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}')"
```

## 性能优化

### 1. GPU层数调整
```bash
# 全部在GPU上（需要足够显存）
python3 run_phi4_gpu1.py --gpu-layers 50

# 部分在GPU上（平衡性能和显存）
python3 run_phi4_gpu1.py --gpu-layers 32

# 纯CPU运行（慢但不需要显存）
python3 run_phi4_gpu1.py --gpu-layers 0
```

### 2. 上下文长度
模型默认上下文长度为4096 tokens，可以在代码中调整 `n_ctx` 参数。

## 故障排除

### 1. 模型文件未找到
```
❌ Phi-4 model not found!
```
**解决方案**：
- 确保模型文件存在
- 使用 `--model` 参数指定完整路径
- 检查文件权限

### 2. CUDA错误
```
CUDA out of memory
```
**解决方案**：
- 减少 `--gpu-layers` 参数
- 关闭其他占用GPU的程序
- 使用 `nvidia-smi` 检查GPU状态

### 3. 依赖问题
```
ImportError: No module named 'llama_cpp'
```
**解决方案**：
- 重新运行安装脚本
- 手动安装：`pip3 install llama-cpp-python`

### 4. 权限问题
```
Permission denied
```
**解决方案**：
```bash
chmod +x install_phi4_dependencies.sh
chmod +x run_phi4_gpu1.py
```

## 示例输出

### 情感分析示例
```bash
$ python3 run_phi4_gpu1.py --text "I love this beautiful sunny day!"

🔧 Set CUDA_VISIBLE_DEVICES=1
✅ Found model: ./models/phi-4-Q4_K_M.gguf
📁 Using model: ./models/phi-4-Q4_K_M.gguf
🎮 GPU layers: 32
🌡️  Temperature: 0.7
🔄 Loading Phi-4 model on GPU 1...
✅ Model loaded successfully!

📝 Analyzing sentiment: I love this beautiful sunny day!
   Sentiment: Positive
   Confidence: 0.9876
   Raw Response: Positive
   Time: 0.15s
```

### 交互模式示例
```bash
$ python3 run_phi4_gpu1.py --interactive

Phi-4 Local Model Interactive Mode
Commands:
  /sentiment <text> - Analyze sentiment
  /generate <prompt> - Generate text
  /quit - Exit

>>> /sentiment I hate waiting in traffic
Text: I hate waiting in traffic
Sentiment: Negative
Confidence: 0.9234
Raw Response: Negative

>>> /generate Once upon a time
Generated: Once upon a time, in a small village nestled between rolling hills...

>>> /quit
Goodbye!
```

## 技术细节

- **模型格式**：GGUF (GPT-Generated Unified Format)
- **量化**：Q4_K_M (4-bit quantization with mixed precision)
- **推理引擎**：llama.cpp (通过 llama-cpp-python)
- **GPU支持**：CUDA (通过 cuBLAS)
- **内存映射**：支持，减少内存使用

## 许可证

请遵守 Phi-4 模型的原始许可证条款。
