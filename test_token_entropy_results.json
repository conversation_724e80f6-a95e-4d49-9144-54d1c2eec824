{"token_entropy_result": {"uncertainty_score": 0.16519237503656198, "token_entropies": [[0.0029629613525484163], [0.004954243209664095], [0.4876599205474734]], "num_responses": 3, "num_valid_responses": 3, "total_tokens": 3, "avg_tokens_per_response": 1.0, "method": "TokenEntropy", "metadata": {"response_token_counts": [1, 1, 1], "entropy_statistics": {"mean": 0.16519237503656198, "std": 0.22802043728831622, "min": 0.0029629613525484163, "max": 0.4876599205474734, "median": 0.004954243209664095}}}, "mean_token_entropy_result": {"uncertainty_score": 1.0983425430942415, "mean_token_entropy": 1.0983425430942415, "response_mean_probs": [0.9979539971719125, 0.9984022577510199, 0.9499249486143209], "prob_statistics": {"mean": 0.9820937345124178, "std": 0.022747502778765152, "min": 0.9499249486143209, "max": 0.9984022577510199}, "num_responses": 3, "num_valid_responses": 3, "method": "MeanTokenEntropy", "metadata": {"token_details": [{"response_id": 0, "mean_prob": 0.9979539971719125, "token_count": 2, "token_probs": [0.9999000049998333, 0.9960079893439915]}, {"response_id": 1, "mean_prob": 0.9984022577510199, "token_count": 2, "token_probs": [0.9998000199986667, 0.997004495503373]}, {"response_id": 2, "mean_prob": 0.9499249486143209, "token_count": 2, "token_probs": [0.9048374180359595, 0.9950124791926823]}]}}, "group_calculation_result": {"group_key": {"input_text": "I love this beautiful sunny day! 😊", "prompt_seed": "sentiment_01", "task_name": "sentiment_analysis", "dataset_source": "twitter_sentiment"}, "meta_info": {"model_identifier": "test_model", "reference_answer": "Positive", "num_responses": 3, "input_text": "I love this beautiful sunny day! 😊"}, "token_entropy": {"uncertainty_score": 0.16519237503656198, "token_entropies": [[0.0029629613525484163], [0.004954243209664095], [0.4876599205474734]], "num_responses": 3, "num_valid_responses": 3, "total_tokens": 3, "avg_tokens_per_response": 1.0, "method": "TokenEntropy", "metadata": {"response_token_counts": [1, 1, 1], "entropy_statistics": {"mean": 0.16519237503656198, "std": 0.22802043728831622, "min": 0.0029629613525484163, "max": 0.4876599205474734, "median": 0.004954243209664095}}}, "mean_token_entropy": {"uncertainty_score": 1.0983425430942415, "mean_token_entropy": 1.0983425430942415, "response_mean_probs": [0.9979539971719125, 0.9984022577510199, 0.9499249486143209], "prob_statistics": {"mean": 0.9820937345124178, "std": 0.022747502778765152, "min": 0.9499249486143209, "max": 0.9984022577510199}, "num_responses": 3, "num_valid_responses": 3, "method": "MeanTokenEntropy", "metadata": {"token_details": [{"response_id": 0, "mean_prob": 0.9979539971719125, "token_count": 2, "token_probs": [0.9999000049998333, 0.9960079893439915]}, {"response_id": 1, "mean_prob": 0.9984022577510199, "token_count": 2, "token_probs": [0.9998000199986667, 0.997004495503373]}, {"response_id": 2, "mean_prob": 0.9499249486143209, "token_count": 2, "token_probs": [0.9048374180359595, 0.9950124791926823]}]}}, "responses_summary": {"response_texts": ["Positive", "Positive", "Negative"], "unique_responses": ["Positive", "Negative"], "response_distribution": {"Positive": 2, "Negative": 1}}, "computation_timestamp": "2025-08-27 08:43:36.936522+00:00"}, "test_timestamp": "2025-01-27"}