#!/usr/bin/env python3
"""
验证采样数据与MongoDB response_collections的一致性
对照sampled_semeval.csv和sampled_commits.csv与数据库中的数据
"""

import pandas as pd
from pymongo import MongoClient
from typing import Dict, List, Any
import sys


def validate_semeval_data():
    """验证SemEval采样数据与response_collections的一致性"""
    print("🔍 验证SemEval采样数据...")
    
    try:
        # 读取采样数据
        sampled_df = pd.read_csv('data/sampled_semeval.csv')
        print(f"📊 采样数据: {len(sampled_df)} 条记录")
        
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询sentiment_analysis任务的数据
        sentiment_docs = list(collection.find({"task_name": "sentiment_analysis"}))
        print(f"📊 MongoDB中sentiment_analysis数据: {len(sentiment_docs)} 条记录")
        
        if not sentiment_docs:
            print("❌ MongoDB中没有找到sentiment_analysis数据")
            client.close()
            return False
        
        # 提取MongoDB中的唯一tweet数据
        mongodb_tweets = {}
        for doc in sentiment_docs:
            # 从task_id中提取tweet_index
            task_id = doc.get('task_id', '')
            if 'twitter_sentiment_' in task_id:
                # 提取tweet ID，格式如: task_twitter_sentiment_802162304137694976_prompt_7_attempt_1
                parts = task_id.split('_')
                if len(parts) >= 4:
                    tweet_id = parts[3]  # 获取tweet ID
                    try:
                        tweet_index = int(tweet_id)
                        mongodb_tweets[tweet_index] = {
                            'text': doc.get('input_text', ''),
                            'validation': doc.get('reference_answer', ''),
                            'tweet_index': tweet_index
                        }
                    except ValueError:
                        continue
        
        print(f"📊 MongoDB中唯一tweet数量: {len(mongodb_tweets)}")
        
        # 对照验证
        matches = 0
        mismatches = []
        
        print("\n🔍 开始对照验证...")
        for idx, row in sampled_df.iterrows():
            sampled_id = row['id']
            sampled_text = row['text'] 
            sampled_label = row['label']
            
            if sampled_id in mongodb_tweets:
                mongodb_record = mongodb_tweets[sampled_id]
                mongodb_text = mongodb_record['text']
                mongodb_label = mongodb_record['validation']
                
                # 检查文本是否一致
                if sampled_text == mongodb_text and sampled_label == mongodb_label:
                    matches += 1
                else:
                    mismatches.append({
                        'id': sampled_id,
                        'sampled_text': sampled_text[:50] + "..." if len(sampled_text) > 50 else sampled_text,
                        'mongodb_text': mongodb_text[:50] + "..." if len(mongodb_text) > 50 else mongodb_text,
                        'sampled_label': sampled_label,
                        'mongodb_label': mongodb_label
                    })
            else:
                mismatches.append({
                    'id': sampled_id,
                    'issue': 'ID not found in MongoDB',
                    'sampled_text': sampled_text[:50] + "..." if len(sampled_text) > 50 else sampled_text
                })
        
        print(f"\n📊 验证结果:")
        print(f"   ✅ 匹配: {matches} 条")
        print(f"   ❌ 不匹配: {len(mismatches)} 条")
        
        if mismatches:
            print(f"\n⚠️  前5个不匹配的记录:")
            for i, mismatch in enumerate(mismatches[:5]):
                print(f"   {i+1}. ID: {mismatch['id']}")
                if 'issue' in mismatch:
                    print(f"      问题: {mismatch['issue']}")
                    print(f"      采样文本: {mismatch['sampled_text']}")
                else:
                    print(f"      采样文本: {mismatch['sampled_text']}")
                    print(f"      MongoDB文本: {mismatch['mongodb_text']}")
                    print(f"      采样标签: {mismatch['sampled_label']}")
                    print(f"      MongoDB标签: {mismatch['mongodb_label']}")
                print()
        
        client.close()
        return matches > 0 and len(mismatches) == 0
        
    except Exception as e:
        print(f"❌ SemEval验证失败: {e}")
        return False


def validate_commits_data():
    """验证Commits采样数据与response_collections的一致性"""
    print("🔍 验证Commits采样数据...")
    
    try:
        # 读取采样数据
        sampled_df = pd.read_csv('data/sampled_commits.csv')
        print(f"📊 采样数据: {len(sampled_df)} 条记录")
        
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询explorative_coding任务的数据
        coding_docs = list(collection.find({"task_name": "explorative_coding"}))
        print(f"📊 MongoDB中explorative_coding数据: {len(coding_docs)} 条记录")
        
        if not coding_docs:
            print("❌ MongoDB中没有找到explorative_coding数据")
            client.close()
            return False
        
        # 提取MongoDB中的唯一commit数据
        mongodb_commits = {}
        for doc in coding_docs:
            # 从task_id中提取commit SHA
            task_id = doc.get('task_id', '')
            if 'pytorch_commits_' in task_id:
                # 提取commit SHA，格式如: task_pytorch_commits_70fb673e51decdd8bf4e55244d910...
                parts = task_id.split('_')
                if len(parts) >= 3:
                    commit_sha = parts[2]  # 获取commit SHA
                    mongodb_commits[commit_sha] = {
                        'message': doc.get('input_text', ''),  # commit message存储在input_text中
                        'date': '',  # 日期信息可能不在这里
                        'author': '',  # 作者信息可能不在这里
                        'sha': commit_sha
                    }
        
        print(f"📊 MongoDB中唯一commit数量: {len(mongodb_commits)}")
        
        # 对照验证
        matches = 0
        mismatches = []
        
        print("\n🔍 开始对照验证...")
        for idx, row in sampled_df.iterrows():
            sampled_sha = row['sha']
            sampled_message = row['message']
            sampled_date = row['date']
            sampled_author = row['author']
            
            if sampled_sha in mongodb_commits:
                mongodb_record = mongodb_commits[sampled_sha]
                mongodb_message = mongodb_record['message']
                mongodb_date = mongodb_record['date']
                mongodb_author = mongodb_record['author']
                
                # 检查是否一致
                if (sampled_message == mongodb_message and 
                    sampled_date == mongodb_date and 
                    sampled_author == mongodb_author):
                    matches += 1
                else:
                    mismatches.append({
                        'sha': sampled_sha,
                        'sampled_message': sampled_message[:50] + "..." if len(sampled_message) > 50 else sampled_message,
                        'mongodb_message': mongodb_message[:50] + "..." if len(mongodb_message) > 50 else mongodb_message,
                        'sampled_author': sampled_author,
                        'mongodb_author': mongodb_author
                    })
            else:
                mismatches.append({
                    'sha': sampled_sha,
                    'issue': 'SHA not found in MongoDB',
                    'sampled_message': sampled_message[:50] + "..." if len(sampled_message) > 50 else sampled_message
                })
        
        print(f"\n📊 验证结果:")
        print(f"   ✅ 匹配: {matches} 条")
        print(f"   ❌ 不匹配: {len(mismatches)} 条")
        
        if mismatches:
            print(f"\n⚠️  前5个不匹配的记录:")
            for i, mismatch in enumerate(mismatches[:5]):
                print(f"   {i+1}. SHA: {mismatch['sha']}")
                if 'issue' in mismatch:
                    print(f"      问题: {mismatch['issue']}")
                    print(f"      采样消息: {mismatch['sampled_message']}")
                else:
                    print(f"      采样消息: {mismatch['sampled_message']}")
                    print(f"      MongoDB消息: {mismatch['mongodb_message']}")
                    print(f"      采样作者: {mismatch['sampled_author']}")
                    print(f"      MongoDB作者: {mismatch['mongodb_author']}")
                print()
        
        client.close()
        return matches > 0 and len(mismatches) == 0
        
    except Exception as e:
        print(f"❌ Commits验证失败: {e}")
        return False


def check_response_collections_structure():
    """检查response_collections的数据结构"""
    print("🔍 检查response_collections数据结构...")
    
    try:
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 获取每个任务的样本文档
        tasks = collection.distinct('task_name')
        print(f"📊 任务类型: {tasks}")
        
        for task in tasks:
            sample_doc = collection.find_one({"task_name": task})
            if sample_doc:
                print(f"\n📋 {task} 样本文档字段:")
                fields = list(sample_doc.keys())
                for field in sorted(fields):
                    value = sample_doc[field]
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"   {field}: {value}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据结构失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 采样数据一致性验证")
    print("=" * 50)
    
    # 检查数据结构
    structure_ok = check_response_collections_structure()
    
    print("\n" + "=" * 50)
    
    # 验证SemEval数据
    semeval_ok = validate_semeval_data()
    
    print("\n" + "-" * 30)
    
    # 验证Commits数据
    commits_ok = validate_commits_data()
    
    print("\n" + "=" * 50)
    print("📋 验证结果总结:")
    print(f"   数据结构检查: {'✅ 正常' if structure_ok else '❌ 异常'}")
    print(f"   SemEval数据一致性: {'✅ 一致' if semeval_ok else '❌ 不一致'}")
    print(f"   Commits数据一致性: {'✅ 一致' if commits_ok else '❌ 不一致'}")
    
    if semeval_ok and commits_ok:
        print("\n🎉 所有采样数据与MongoDB一致！")
    else:
        print("\n⚠️  存在数据不一致，请检查具体问题")
    
    return 0 if (semeval_ok and commits_ok) else 1


if __name__ == "__main__":
    exit(main())
