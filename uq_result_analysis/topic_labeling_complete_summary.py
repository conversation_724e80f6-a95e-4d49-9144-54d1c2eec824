#!/usr/bin/env python3
"""
Topic Labeling 完整图表生成总结
包含所有四种类型的图表
"""

from pathlib import Path

def main():
    """总结所有生成的图表类型"""
    
    results_dir = Path('uq_result_analysis/figures/topic_labeling_scatter')
    
    print("=== 🎯 Topic Labeling 完整图表生成总结 ===")
    print()
    
    print("📊 **四种图表类型全部完成**")
    print()
    
    print("1️⃣ **纯拟合线图** (2x2组合)")
    print("   📄 topic_labeling_fitting_lines_only.pdf/png")
    print("   ✨ 特点: 只显示拟合线，清晰展示趋势关系")
    print("   🎯 用途: 比较不同方法的线性关系斜率")
    print()
    
    print("2️⃣ **纯散点图** (2x2组合)")
    print("   📄 topic_labeling_scatter_only.pdf/png")
    print("   ✨ 特点: 只显示散点，清晰展示数据分布")
    print("   🎯 用途: 观察数据聚集程度和离散情况")
    print()
    
    print("3️⃣ **合并图** (2x2组合)")
    print("   📄 topic_labeling_scatter_combined.pdf/png")
    print("   ✨ 特点: 散点+拟合线+凸包框定")
    print("   🎯 用途: 完整的分析视图，包含所有信息")
    print()
    
    print("4️⃣ **个别方法图** (每个方法单独一图)")
    print("   📄 individual_{Method}_{LLM}_{Embedding}.pdf/png")
    print("   ✨ 特点: 每个方法单独展示，散点+拟合线")
    print("   🎯 用途: 深入分析特定方法的表现")
    print("   📊 数量: 24张图 (6方法 × 2LLM × 2Embedding)")
    print()
    
    print("🔄 **坐标交换完成**")
    print("   📐 x轴: Embedding Reference Distance")
    print("   📐 y轴: UQ Value (Original Scale)")
    print("   🔧 拟合线: 自动计算反函数系数")
    print()
    
    print("📈 **个别方法图详细信息**")
    print()
    
    methods = [
        "EigValLaplacianJaccard", "EigValLaplacianNLI", 
        "EccentricityJaccard", "EccentricityNLIEntail",
        "SemanticEntropyNLI", "EmbeddingQwen"
    ]
    
    llm_models = ["gpt-oss:20b", "phi4:latest"]
    embedding_models = ["E5", "Qwen"]
    
    print("   🔬 **UQ方法** (6个):")
    for i, method in enumerate(methods, 1):
        print(f"      {i}. {method}")
    
    print()
    print("   🤖 **LLM模型** (2个):")
    for i, model in enumerate(llm_models, 1):
        print(f"      {i}. {model}")
    
    print()
    print("   🔤 **Embedding模型** (2个):")
    for i, model in enumerate(embedding_models, 1):
        print(f"      {i}. {model}")
    
    print()
    print("   📊 **组合总数**: 6 × 2 × 2 = 24张个别图")
    print()
    
    if results_dir.exists():
        files = list(results_dir.glob('*'))
        total_size = sum(f.stat().st_size for f in files) / (1024*1024)
        
        # 分类统计文件
        individual_files = [f for f in files if f.name.startswith('individual_')]
        combined_files = [f for f in files if not f.name.startswith('individual_')]
        
        print("📁 **文件统计**")
        print(f"   📂 位置: {results_dir.absolute()}")
        print(f"   📊 总文件数: {len(files)}")
        print(f"   💾 总大小: {total_size:.1f} MB")
        print()
        
        print("   📈 **组合图表** (8个文件):")
        for file in sorted(combined_files):
            size_kb = file.stat().st_size / 1024
            format_type = "📄 PDF" if file.suffix == '.pdf' else "🖼️ PNG"
            print(f"      {format_type} {file.name} ({size_kb:.0f}KB)")
        
        print()
        print(f"   🔬 **个别方法图** ({len(individual_files)}个文件):")
        
        # 按方法分组显示
        for method in methods:
            method_files = [f for f in individual_files if method in f.name]
            if method_files:
                print(f"      🔹 {method} ({len(method_files)}个文件):")
                for file in sorted(method_files):
                    size_kb = file.stat().st_size / 1024
                    format_type = "📄" if file.suffix == '.pdf' else "🖼️"
                    # 提取模型信息
                    parts = file.stem.split('_')
                    if len(parts) >= 4:
                        llm_part = parts[2] + ':' + parts[3] if parts[2] == 'gpt-oss' else parts[2]
                        emb_part = parts[-1]
                        print(f"         {format_type} {llm_part}+{emb_part} ({size_kb:.0f}KB)")
    
    print()
    print("🎯 **使用建议**")
    print()
    print("   📊 **概览分析**: 使用2x2组合图")
    print("      - 拟合线图: 比较趋势")
    print("      - 散点图: 观察分布")
    print("      - 合并图: 完整分析")
    print()
    print("   🔍 **深入分析**: 使用个别方法图")
    print("      - 每个方法的详细表现")
    print("      - 不同模型组合的对比")
    print("      - 统计信息清晰显示")
    print()
    print("   📄 **论文发表**: 推荐PDF格式")
    print("   🎨 **演示展示**: 推荐PNG格式")
    print()
    
    print("✅ **所有图表类型已完成！**")
    print("🎉 **特色功能**:")
    print("   - 坐标交换 (x=Reference Distance, y=UQ Value)")
    print("   - 四种图表类型 (拟合线/散点/合并/个别)")
    print("   - 24张个别方法图 (每个方法单独分析)")
    print("   - ACM格式字体 (Times New Roman)")
    print("   - 完整统计信息 (公式+R²+显著性)")

if __name__ == "__main__":
    main()
