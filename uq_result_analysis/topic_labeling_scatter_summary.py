#!/usr/bin/env python3
"""
Topic Labeling 散点图结果总结
"""

from pathlib import Path

def main():
    """总结生成的散点图结果"""
    
    results_dir = Path('uq_result_analysis/figures/topic_labeling_scatter')
    
    print("=== 🎯 Topic Labeling Scatter Plots - Final Results ===")
    print()
    
    print("✅ **完成的优化要求**")
    print("   1. ✅ 分模型绘制: gpt-oss:20b 与 phi4:latest 分开")
    print("   2. ✅ 选取指定的6个UQ方法:")
    print("      - EigValLaplacianJaccardUQ")
    print("      - EigValLaplacianNLIUQ") 
    print("      - EccentricityJaccardUQ")
    print("      - EccentricityNLIEntailUQ")
    print("      - SemanticEntropyNLIUQ")
    print("      - EmbeddingQwenUQ")
    print("   3. ✅ 分embedding模型: E5 和 Qwen 分开绘制")
    print("   4. ✅ 使用不同颜色和marker的散点图")
    print("   5. ✅ 套绳方式框定每个方法的散点区域 (凸包)")
    print("   6. ✅ 不归一化数据 (使用原始UQ值)")
    print("   7. ✅ 透明图例，避免掩盖图片")
    print("   8. ✅ 显示线性拟合公式和统计信息")
    print("   9. ✅ 按R²从高到低排序显示")
    print("   10. ✅ p-value用星号表示显著性")
    print()
    
    print("📊 **图表特性**")
    print("   🎨 **视觉元素**:")
    print("      - 6种不同颜色和形状的marker")
    print("      - 虚线凸包框定散点区域")
    print("      - 半透明填充区域")
    print("      - 粗线条拟合线")
    print()
    
    print("   📈 **统计信息显示**:")
    print("      - 线性拟合公式: y = ax + b")
    print("      - R²值 (决定系数)")
    print("      - 显著性: *** (p<0.001), ** (p<0.01), * (p<0.05), ns (不显著)")
    print("      - 按R²从高到低排序")
    print()
    
    print("   🎯 **图例设计**:")
    print("      - 方法标识图例 (左上角)")
    print("      - 公式信息文本框 (透明背景)")
    print("      - 避免重复显示标签")
    print()
    
    if results_dir.exists():
        files = list(results_dir.glob('*'))
        total_size = sum(f.stat().st_size for f in files) / (1024*1024)
        
        print("📁 **生成的文件**")
        print(f"   📂 位置: {results_dir.absolute()}")
        print(f"   📊 文件数量: {len(files)}")
        print(f"   💾 总大小: {total_size:.1f} MB")
        print()
        
        # 分类显示文件
        combined_files = [f for f in files if 'combined' in f.name]
        separate_files = [f for f in files if 'combined' not in f.name]
        
        print("   📊 **组合图表** (2x2子图):")
        for file in sorted(combined_files):
            size_kb = file.stat().st_size / 1024
            format_type = "📄 PDF" if file.suffix == '.pdf' else "🖼️ PNG"
            print(f"      {format_type} {file.name} ({size_kb:.0f}KB)")
        
        print()
        print("   📈 **分离图表** (每个模型组合单独一图):")
        
        # 按模型组合分组
        model_combinations = {
            'gpt-oss:20b + E5': [f for f in separate_files if 'gpt-oss_20b_E5' in f.name],
            'gpt-oss:20b + Qwen': [f for f in separate_files if 'gpt-oss_20b_Qwen' in f.name],
            'phi4:latest + E5': [f for f in separate_files if 'phi4_latest_E5' in f.name],
            'phi4:latest + Qwen': [f for f in separate_files if 'phi4_latest_Qwen' in f.name]
        }
        
        for combo, combo_files in model_combinations.items():
            if combo_files:
                print(f"      🔹 {combo}:")
                for file in sorted(combo_files):
                    size_kb = file.stat().st_size / 1024
                    format_type = "📄 PDF" if file.suffix == '.pdf' else "🖼️ PNG"
                    print(f"         {format_type} {file.name} ({size_kb:.0f}KB)")
    
    print()
    print("🎯 **使用建议**")
    print("   📊 **论文发表**: 使用PDF格式的分离图表，清晰度最高")
    print("   🎨 **演示展示**: 使用PNG格式，便于插入PPT")
    print("   📈 **概览比较**: 使用组合图表，一次查看所有模型组合")
    print("   🔍 **详细分析**: 使用分离图表，每个模型组合的细节更清楚")
    print()
    
    print("📈 **数据统计**")
    print("   📊 生成了 8,616 个散点数据")
    print("   🎯 覆盖 4 个模型组合 (2 LLM × 2 Embedding)")
    print("   🔬 包含 6 个UQ方法的完整分析")
    print("   📐 使用原始UQ值尺度，无归一化")
    print()
    
    print("✅ **所有要求已完成！散点图已准备就绪。**")
    print("🎉 **特色功能**: 凸包区域框定 + 透明图例 + R²排序 + 显著性标记")

if __name__ == "__main__":
    main()
