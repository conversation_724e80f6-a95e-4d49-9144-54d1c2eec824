#!/usr/bin/env python3
"""
Explorative Coding UQ Results Violin Plot Analysis
专门为 explorative_coding 任务生成指定 UQ 方法的合并 violin 图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

def setup_paper_style():
    """设置ACM论文风格的图表样式"""
    plt.style.use('default')
    plt.rcParams.update({
        'font.family': 'serif',
        'font.serif': ['Times New Roman', 'Times', 'DejaVu Serif', 'Bitstream Vera Serif', 'Computer Modern Roman', 'New Century Schoolbook', 'Century Schoolbook L', 'Utopia', 'ITC Bookman', 'Bookman', 'Nimbus Roman No9 L', 'Palatino', 'Charter', 'serif'],
        'font.size': 16,  # 增大基础字体
        'axes.titlesize': 20,  # 增大标题字体
        'axes.labelsize': 18,  # 增大轴标签字体
        'xtick.labelsize': 14,  # 增大x轴刻度字体
        'ytick.labelsize': 14,  # 增大y轴刻度字体
        'legend.fontsize': 14,  # 增大图例字体
        'figure.titlesize': 22,  # 增大图表标题字体
        'lines.linewidth': 2,
        'lines.markersize': 8,  # 增大标记点
        'axes.linewidth': 1.2,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.linewidth': 0.8,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })

def load_and_preprocess_data(csv_path: str, specified_methods: list):
    """加载和预处理 explorative_coding 数据"""
    print(f"Loading data from: {csv_path}")
    
    # 加载数据
    df = pd.read_csv(csv_path)
    print(f"Original data shape: {df.shape}")
    
    # 过滤指定的 UQ 方法
    df_filtered = df[df['uq_method'].isin(specified_methods)].copy()
    print(f"Filtered data shape: {df_filtered.shape}")
    
    # 移除空值
    df_clean = df_filtered.dropna(subset=['uq_value']).copy()
    print(f"Clean data shape: {df_clean.shape}")
    
    # 过滤掉 unknown 模型
    df_analysis = df_clean[df_clean['llm_model'] != 'unknown'].copy()
    print(f"Analysis data shape: {df_analysis.shape}")
    
    print(f"Available models: {df_analysis['llm_model'].unique()}")
    print(f"Available UQ methods: {sorted(df_analysis['uq_method'].unique())}")
    
    return df_analysis

def normalize_uq_values(df):
    """对每个 UQ 方法的值进行 Min-Max 归一化到 [0,1]"""
    print("Normalizing UQ values...")
    
    df_normalized = df.copy()
    df_normalized['uq_value_normalized'] = np.nan
    
    normalization_stats = {}
    
    for method in df['uq_method'].unique():
        method_mask = df['uq_method'] == method
        method_values = df.loc[method_mask, 'uq_value']
        
        # 计算统计信息
        min_val = method_values.min()
        max_val = method_values.max()
        mean_val = method_values.mean()
        std_val = method_values.std()
        
        # Min-Max 归一化到 [0,1]
        if max_val != min_val:
            normalized_values = (method_values - min_val) / (max_val - min_val)
        else:
            normalized_values = method_values * 0  # 如果所有值相同，归一化为0
        
        df_normalized.loc[method_mask, 'uq_value_normalized'] = normalized_values
        
        # 保存归一化统计信息
        normalization_stats[method] = {
            'min': float(min_val),
            'max': float(max_val),
            'mean': float(mean_val),
            'std': float(std_val),
            'range': float(max_val - min_val),
            'sample_count': len(method_values)
        }
        
        print(f"  {method}: [{min_val:.3f}, {max_val:.3f}] -> [0, 1], samples: {len(method_values)}")
    
    return df_normalized, normalization_stats

def create_combined_violin_plot(df, specified_methods, output_dir, use_normalized=True):
    """创建合并的 violin 图"""
    print("Creating combined violin plot...")
    
    setup_paper_style()
    
    # 选择使用原始值还是归一化值
    value_col = 'uq_value_normalized' if use_normalized else 'uq_value'
    y_label = 'Normalized UQ Value [0,1]' if use_normalized else 'UQ Value'
    
    # 确保方法按指定顺序排列
    df_plot = df[df['uq_method'].isin(specified_methods)].copy()
    
    # 创建图表 - 增大尺寸以适应更大字体和标注
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    
    # 准备数据 - 按指定顺序
    method_data = []
    method_labels = []
    method_stats = []  # 存储统计信息用于标注

    for method in specified_methods:
        method_values = df_plot[df_plot['uq_method'] == method][value_col].values
        if len(method_values) > 0:
            method_data.append(method_values)
            # 特殊处理 LUQUQ -> LUQ，其他方法去掉 UQ 后缀
            if method == 'LUQUQ':
                clean_label = 'LUQ'
            else:
                clean_label = method.replace('UQ', '') if method.endswith('UQ') else method
            method_labels.append(clean_label)

            # 计算统计信息
            mean_val = np.mean(method_values)
            median_val = np.median(method_values)
            method_stats.append({'mean': mean_val, 'median': median_val})

            print(f"  {method}: {len(method_values)} samples")
    
    if not method_data:
        print("No data available for violin plot")
        return
    
    # 创建 violin 图
    try:
        parts = ax.violinplot(method_data, positions=range(len(method_labels)), 
                             showmeans=True, showmedians=True, showextrema=True)
        
        # 设置 violin 图样式
        if 'bodies' in parts:
            for pc in parts['bodies']:
                pc.set_alpha(0.7)
                pc.set_facecolor('lightblue')
                pc.set_edgecolor('navy')
                pc.set_linewidth(1.2)
        
        # 设置均值和中位数的样式
        if 'cmeans' in parts:
            parts['cmeans'].set_color('red')
            parts['cmeans'].set_linewidth(2)
        if 'cmedians' in parts:
            parts['cmedians'].set_color('orange')
            parts['cmedians'].set_linewidth(2)
        if 'cbars' in parts:
            parts['cbars'].set_color('black')
            parts['cbars'].set_linewidth(1)
        if 'cmins' in parts:
            parts['cmins'].set_color('black')
            parts['cmins'].set_linewidth(1)
        if 'cmaxes' in parts:
            parts['cmaxes'].set_color('black')
            parts['cmaxes'].set_linewidth(1)
            
    except Exception as e:
        print(f"Error creating violin plot: {e}")
        plt.close()
        return
    
    # 设置图表属性 - 不要 x 轴标题
    ax.set_ylabel(y_label, fontweight='bold')
    ax.set_title('Explorative Coding - UQ Methods Comparison\n(Violin Plot)',
                fontweight='bold', pad=20)

    # 设置 x 轴标签
    ax.set_xticks(range(len(method_labels)))
    ax.set_xticklabels(method_labels, rotation=45, ha='right')

    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)

    # 用箭头标注均值和中位数的具体数值
    for i, stats in enumerate(method_stats):
        mean_val = stats['mean']
        median_val = stats['median']

        # 获取当前 violin 的 y 范围来确定标注位置
        y_max = ax.get_ylim()[1]
        y_min = ax.get_ylim()[0]
        y_range = y_max - y_min

        # 均值标注 (红色箭头指向均值线)
        ax.annotate(f'μ={mean_val:.3f}',
                   xy=(i, mean_val),
                   xytext=(i + 0.3, mean_val + y_range * 0.08),
                   ha='left', va='center', color='red', fontweight='bold',
                   arrowprops=dict(arrowstyle='->', color='red', lw=1.5))

        # 中位数标注 (橙色箭头指向中位数线)
        ax.annotate(f'M={median_val:.3f}',
                   xy=(i, median_val),
                   xytext=(i - 0.3, median_val - y_range * 0.08),
                   ha='right', va='center', color='orange', fontweight='bold',
                   arrowprops=dict(arrowstyle='->', color='orange', lw=1.5))

    # 添加图例 - 放在左上角避免遮挡
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='red', linewidth=3, label='Mean'),
        Line2D([0], [0], color='orange', linewidth=3, label='Median'),
        Line2D([0], [0], color='lightblue', alpha=0.7, linewidth=6, label='Distribution')
    ]
    ax.legend(handles=legend_elements, loc='upper left', frameon=True,
             fancybox=True, shadow=True, fontsize=12)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    suffix = 'normalized' if use_normalized else 'original'
    filename = f'explorative_coding_combined_violin_{suffix}.pdf'
    output_path = output_dir / filename
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Violin plot saved to: {output_path}")
    
    # 同时保存 PNG 格式
    png_filename = f'explorative_coding_combined_violin_{suffix}.png'
    png_output_path = output_dir / png_filename
    plt.savefig(png_output_path, format='png', bbox_inches='tight', dpi=300)
    print(f"Violin plot (PNG) saved to: {png_output_path}")
    
    plt.close()

def create_clean_violin_plot(df, specified_methods, output_dir, use_normalized=True):
    """创建简洁的 violin 图（无数值标注）"""
    print("Creating clean violin plot (no annotations)...")

    setup_paper_style()

    # 选择使用原始值还是归一化值
    value_col = 'uq_value_normalized' if use_normalized else 'uq_value'
    y_label = 'Normalized UQ Value [0,1]' if use_normalized else 'UQ Value'

    # 确保方法按指定顺序排列
    df_plot = df[df['uq_method'].isin(specified_methods)].copy()

    # 创建图表 - 增大尺寸以适应更大字体
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # 准备数据 - 按指定顺序
    method_data = []
    method_labels = []

    for method in specified_methods:
        method_values = df_plot[df_plot['uq_method'] == method][value_col].values
        if len(method_values) > 0:
            method_data.append(method_values)
            # 特殊处理 LUQUQ -> LUQ，其他方法去掉 UQ 后缀
            if method == 'LUQUQ':
                clean_label = 'LUQ'
            else:
                clean_label = method.replace('UQ', '') if method.endswith('UQ') else method
            method_labels.append(clean_label)
            print(f"  {method}: {len(method_values)} samples")

    if not method_data:
        print("No data available for violin plot")
        return

    # 创建 violin 图
    try:
        parts = ax.violinplot(method_data, positions=range(len(method_labels)),
                             showmeans=True, showmedians=True, showextrema=True)

        # 设置 violin 图样式
        if 'bodies' in parts:
            for pc in parts['bodies']:
                pc.set_alpha(0.7)
                pc.set_facecolor('lightblue')
                pc.set_edgecolor('navy')
                pc.set_linewidth(1.2)

        # 设置均值和中位数的样式
        if 'cmeans' in parts:
            parts['cmeans'].set_color('red')
            parts['cmeans'].set_linewidth(2)
        if 'cmedians' in parts:
            parts['cmedians'].set_color('orange')
            parts['cmedians'].set_linewidth(2)
        if 'cbars' in parts:
            parts['cbars'].set_color('black')
            parts['cbars'].set_linewidth(1)
        if 'cmins' in parts:
            parts['cmins'].set_color('black')
            parts['cmins'].set_linewidth(1)
        if 'cmaxes' in parts:
            parts['cmaxes'].set_color('black')
            parts['cmaxes'].set_linewidth(1)

    except Exception as e:
        print(f"Error creating violin plot: {e}")
        plt.close()
        return

    # 设置图表属性 - 不要 x 轴标题
    ax.set_ylabel(y_label, fontweight='bold')
    # ax.set_title('UQ Methods Comparison (Violin Plot)',
                # fontweight='bold', pad=20)

    # 设置 x 轴标签
    ax.set_xticks(range(len(method_labels)))
    ax.set_xticklabels(method_labels, rotation=45, ha='right')

    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='--')
    ax.set_axisbelow(True)

    # 添加图例 - 放在左上角避免遮挡
    from matplotlib.lines import Line2D
    legend_elements = [
        Line2D([0], [0], color='red', linewidth=3, label='Mean'),
        Line2D([0], [0], color='orange', linewidth=3, label='Median'),
        # Line2D([0], [0], color='lightblue', alpha=0.7, linewidth=6, label='Distribution')
    ]
    ax.legend(handles=legend_elements, loc='upper right', frameon=True,
             fancybox=True, shadow=True, fontsize=12, framealpha=0.4)

    # 调整布局
    plt.tight_layout()

    # 保存图表
    suffix = 'normalized_clean' if use_normalized else 'original_clean'
    filename = f'explorative_coding_combined_violin_{suffix}.pdf'
    output_path = output_dir / filename
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Clean violin plot saved to: {output_path}")

    # 同时保存 PNG 格式
    png_filename = f'explorative_coding_combined_violin_{suffix}.png'
    png_output_path = output_dir / png_filename
    plt.savefig(png_output_path, format='png', bbox_inches='tight', dpi=300)
    print(f"Clean violin plot (PNG) saved to: {png_output_path}")

    plt.close()

def generate_summary_statistics(df, specified_methods, output_dir):
    """生成汇总统计信息"""
    print("Generating summary statistics...")
    
    stats_list = []
    
    for method in specified_methods:
        method_data = df[df['uq_method'] == method]
        
        if len(method_data) == 0:
            continue
            
        # 原始值统计
        orig_values = method_data['uq_value']
        # 归一化值统计
        norm_values = method_data['uq_value_normalized']
        
        stats = {
            'uq_method': method,
            'sample_count': len(method_data),
            'original_mean': orig_values.mean(),
            'original_std': orig_values.std(),
            'original_min': orig_values.min(),
            'original_max': orig_values.max(),
            'original_median': orig_values.median(),
            'original_q25': orig_values.quantile(0.25),
            'original_q75': orig_values.quantile(0.75),
            'normalized_mean': norm_values.mean(),
            'normalized_std': norm_values.std(),
            'normalized_min': norm_values.min(),
            'normalized_max': norm_values.max(),
            'normalized_median': norm_values.median(),
            'normalized_q25': norm_values.quantile(0.25),
            'normalized_q75': norm_values.quantile(0.75)
        }
        
        stats_list.append(stats)
    
    # 保存统计信息
    stats_df = pd.DataFrame(stats_list)
    stats_path = output_dir / 'explorative_coding_uq_statistics.csv'
    stats_df.to_csv(stats_path, index=False)
    print(f"Statistics saved to: {stats_path}")
    
    # 打印汇总信息
    print("\nSummary Statistics:")
    print("=" * 80)
    for _, row in stats_df.iterrows():
        print(f"{row['uq_method']}:")
        print(f"  Samples: {row['sample_count']}")
        print(f"  Original: {row['original_mean']:.3f} ± {row['original_std']:.3f} "
              f"[{row['original_min']:.3f}, {row['original_max']:.3f}]")
        print(f"  Normalized: {row['normalized_mean']:.3f} ± {row['normalized_std']:.3f} "
              f"[{row['normalized_min']:.3f}, {row['normalized_max']:.3f}]")
        print()
    
    return stats_df

def main():
    """主函数"""
    print("=== Explorative Coding UQ Results Violin Plot Analysis ===")

    # 指定的 UQ 方法
    specified_methods = [
        "EigValLaplacianJaccardUQ",
        "EigValLaplacianNLIUQ",
        "EccentricityJaccardUQ",
        "EccentricityNLIEntailUQ",
        "SemanticEntropyNLIUQ",
        "EmbeddingQwenUQ",
        "EmbeddingE5UQ",
        "LUQUQ",
        "LofreeCPUQ"
    ]

    print(f"Target UQ methods: {specified_methods}")

    # 数据路径
    data_dir = Path('uq_result_analysis/data')
    csv_path = data_dir / 'UQ_result_explorative_coding.csv'

    if not csv_path.exists():
        print(f"Error: Data file not found: {csv_path}")
        return

    # 创建输出目录
    output_dir = Path('uq_result_analysis/figures/explorative_coding_violin')
    output_dir.mkdir(parents=True, exist_ok=True)

    # 加载和预处理数据
    df = load_and_preprocess_data(str(csv_path), specified_methods)

    if df.empty:
        print("No data available for analysis")
        return

    # 归一化 UQ 值
    df_normalized, norm_stats = normalize_uq_values(df)

    # 创建合并的 violin 图 (归一化版本)
    create_combined_violin_plot(df_normalized, specified_methods, output_dir, use_normalized=True)

    # 创建合并的 violin 图 (原始值版本)
    create_combined_violin_plot(df_normalized, specified_methods, output_dir, use_normalized=False)

    # 创建简洁版本的 violin 图 (归一化版本，无数值标注)
    create_clean_violin_plot(df_normalized, specified_methods, output_dir, use_normalized=True)

    # 创建简洁版本的 violin 图 (原始值版本，无数值标注)
    create_clean_violin_plot(df_normalized, specified_methods, output_dir, use_normalized=False)

    # 生成汇总统计信息
    stats_df = generate_summary_statistics(df_normalized, specified_methods, output_dir)

    print(f"\nAnalysis completed! Results saved to: {output_dir}")
    print(f"Generated files:")
    print(f"  - explorative_coding_combined_violin_normalized.pdf/png (with annotations)")
    print(f"  - explorative_coding_combined_violin_original.pdf/png (with annotations)")
    print(f"  - explorative_coding_combined_violin_normalized_clean.pdf/png (clean version)")
    print(f"  - explorative_coding_combined_violin_original_clean.pdf/png (clean version)")
    print(f"  - explorative_coding_uq_statistics.csv")

if __name__ == "__main__":
    main()
