#!/usr/bin/env python3
"""
最终结果总结 - ACM格式字体 + 简洁版本
"""

from pathlib import Path

def main():
    """总结最终生成的所有版本"""
    
    results_dir = Path('uq_result_analysis/figures/explorative_coding_violin')
    
    print("=== 🎯 Final Explorative Coding Violin Plot Results ===")
    print()
    
    print("📊 **Generated Versions:**")
    print()
    
    print("1️⃣ **带数值标注版本 (With Annotations)**")
    print("   📄 explorative_coding_combined_violin_normalized.pdf/png")
    print("   📄 explorative_coding_combined_violin_original.pdf/png")
    print("   ✨ 特点: 箭头指向均值(μ)和中位数(M)，精确数值标注")
    print()
    
    print("2️⃣ **简洁版本 (Clean Version)**")
    print("   📄 explorative_coding_combined_violin_normalized_clean.pdf/png")
    print("   📄 explorative_coding_combined_violin_original_clean.pdf/png")
    print("   ✨ 特点: 无数值标注，纯净视觉效果")
    print()
    
    print("📈 **统计数据文件:**")
    print("   📊 explorative_coding_uq_statistics.csv")
    print()
    
    print("🎨 **ACM格式字体设置:**")
    print("   ✅ font.family: 'serif'")
    print("   ✅ font.serif: ['Times New Roman', 'Times', 'DejaVu Serif', ...]")
    print("   ✅ 符合ACM论文发表标准")
    print()
    
    print("🏷️ **方法标签优化:**")
    final_labels = [
        "EigValLaplacianJaccard", "EigValLaplacianNLI", 
        "EccentricityJaccard", "EccentricityNLIEntail",
        "SemanticEntropyNLI", "EmbeddingQwen", "EmbeddingE5",
        "LUQ", "LofreeCPU"
    ]
    for i, label in enumerate(final_labels, 1):
        print(f"   {i:2d}. {label}")
    print()
    
    print("📏 **图表规格:**")
    print("   📐 尺寸: 16×10 英寸")
    print("   🔤 字体大小: 基础16pt, 标题20pt, 轴标签18pt")
    print("   🎨 图例位置: 左上角 (避免遮挡)")
    print("   📊 显示: 均值线(红)、中位数线(橙)、分布形状")
    print()
    
    if results_dir.exists():
        files = list(results_dir.glob('*'))
        total_size = sum(f.stat().st_size for f in files) / (1024*1024)
        print(f"📁 **文件信息:**")
        print(f"   📂 位置: {results_dir.absolute()}")
        print(f"   📊 文件数量: {len(files)}")
        print(f"   💾 总大小: {total_size:.1f} MB")
        print()
        
        print("📋 **文件列表:**")
        for file in sorted(files):
            size_kb = file.stat().st_size / 1024
            if 'clean' in file.name:
                version = "🧹 简洁版"
            elif 'normalized' in file.name:
                version = "📊 归一化版"
            elif 'original' in file.name:
                version = "📈 原始值版"
            else:
                version = "📋 统计数据"
            
            format_type = "📄 PDF" if file.suffix == '.pdf' else "🖼️ PNG" if file.suffix == '.png' else "📊 CSV"
            print(f"   {format_type} {file.name} ({size_kb:.0f}KB) - {version}")
    
    print()
    print("🎯 **使用建议:**")
    print("   📊 **论文发表**: 使用PDF格式的归一化版本")
    print("   🎨 **演示展示**: 使用PNG格式，根据需要选择带标注或简洁版")
    print("   📈 **方法比较**: 推荐归一化版本，便于跨方法比较")
    print("   🔍 **详细分析**: 查看CSV统计文件获取精确数值")
    print()
    
    print("✅ **所有优化已完成！**")
    print("   🎨 ACM格式字体")
    print("   📏 优化的字体大小")
    print("   🏷️ 清洁的方法标签")
    print("   📊 两种版本可选")
    print("   🎯 专业的视觉效果")

if __name__ == "__main__":
    main()
