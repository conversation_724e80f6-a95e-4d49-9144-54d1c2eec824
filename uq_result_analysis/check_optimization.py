#!/usr/bin/env python3
"""
检查优化后的 violin 图效果
"""

from pathlib import Path
import pandas as pd

def main():
    """检查优化效果"""
    
    results_dir = Path('uq_result_analysis/figures/explorative_coding_violin')
    
    print("=== Violin Plot Optimization Check ===")
    
    # 检查文件大小变化
    files = list(results_dir.glob('*.png'))
    print(f"\nGenerated PNG files:")
    for file in sorted(files):
        size_kb = file.stat().st_size / 1024
        print(f"  {file.name}: {size_kb:.1f} KB")
    
    # 检查统计数据
    stats_file = results_dir / 'explorative_coding_uq_statistics.csv'
    if stats_file.exists():
        df = pd.read_csv(stats_file)
        print(f"\n=== Optimization Summary ===")
        print("✅ 1. 横坐标标签已去掉 'UQ' 后缀")
        print("✅ 2. 移除了横坐标标题")
        print("✅ 3. 字体大小已增大:")
        print("   - 基础字体: 12 → 16")
        print("   - 轴标签: 12 → 18") 
        print("   - 刻度标签: 10 → 14")
        print("   - 图例: 10 → 14")
        print("✅ 4. 图表尺寸增大: (14,8) → (16,10)")
        print("✅ 5. 图例位置调整: upper right → upper left")
        print("✅ 6. 添加了均值和中位数数值标注")
        
        print(f"\n=== Method Labels (UQ removed) ===")
        methods = [
            "EigValLaplacianJaccard", "EigValLaplacianNLI", 
            "EccentricityJaccard", "EccentricityNLIEntail",
            "SemanticEntropyNLI", "EmbeddingQwen", "EmbeddingE5",
            "LUQ", "LofreeCPU"
        ]
        for i, method in enumerate(methods, 1):
            print(f"  {i}. {method}")
        
        print(f"\n=== Value Annotations Added ===")
        print("Each violin plot now shows:")
        print("  🔴 Mean values (red boxes above violin)")
        print("  🟠 Median values (orange boxes below violin)")
        print("  📊 Both with 3 decimal precision")
        
        print(f"\n=== Files Ready for Use ===")
        print(f"📁 Location: {results_dir.absolute()}")
        print("📊 Normalized version: Best for comparing methods")
        print("📊 Original version: Shows actual UQ value ranges")
        print("📈 Both available in PDF (vector) and PNG (raster) formats")

if __name__ == "__main__":
    main()
