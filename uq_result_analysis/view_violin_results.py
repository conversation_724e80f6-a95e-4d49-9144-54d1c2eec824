#!/usr/bin/env python3
"""
简单脚本用于查看生成的 violin 图结果
"""

from pathlib import Path
import webbrowser
import os

def main():
    """显示生成的结果文件"""
    
    results_dir = Path('uq_result_analysis/figures/explorative_coding_violin')
    
    print("=== Explorative Coding UQ Violin Plot Results ===")
    print(f"Results directory: {results_dir.absolute()}")
    
    if not results_dir.exists():
        print("Results directory not found!")
        return
    
    # 列出所有生成的文件
    files = list(results_dir.glob('*'))
    files.sort()
    
    print(f"\nGenerated files ({len(files)}):")
    for i, file in enumerate(files, 1):
        print(f"  {i}. {file.name}")
        print(f"     Size: {file.stat().st_size / 1024:.1f} KB")
    
    # 显示统计信息
    stats_file = results_dir / 'explorative_coding_uq_statistics.csv'
    if stats_file.exists():
        print(f"\n=== Summary Statistics ===")
        with open(stats_file, 'r') as f:
            lines = f.readlines()
            print(f"Statistics file contains {len(lines)-1} UQ methods")
    
    # 提供文件路径信息
    print(f"\n=== File Locations ===")
    print(f"Normalized violin plot (PDF): {(results_dir / 'explorative_coding_combined_violin_normalized.pdf').absolute()}")
    print(f"Normalized violin plot (PNG): {(results_dir / 'explorative_coding_combined_violin_normalized.png').absolute()}")
    print(f"Original violin plot (PDF): {(results_dir / 'explorative_coding_combined_violin_original.pdf').absolute()}")
    print(f"Original violin plot (PNG): {(results_dir / 'explorative_coding_combined_violin_original.png').absolute()}")
    print(f"Statistics CSV: {stats_file.absolute()}")
    
    print(f"\n=== Analysis Summary ===")
    print("✓ Successfully generated violin plots for 9 specified UQ methods")
    print("✓ Each method has 202 samples from explorative_coding task")
    print("✓ Generated both normalized [0,1] and original value versions")
    print("✓ Included comprehensive statistics in CSV format")
    
    print(f"\n=== Key Findings ===")
    print("• All 9 specified UQ methods are available in the data")
    print("• Data comes from qwen3-32b model only")
    print("• Normalization helps compare methods with different scales")
    print("• LofreeCPUQ shows highest normalized mean (0.77)")
    print("• EigValLaplacianNLIUQ shows lowest normalized mean (0.36)")

if __name__ == "__main__":
    main()
