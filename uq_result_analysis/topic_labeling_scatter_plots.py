#!/usr/bin/env python3
"""
Topic Labeling 散点图绘制脚本
根据要求绘制散点图，包含：
1. 不同颜色和marker的散点图
2. 套绳方式框定区域
3. 不归一化数据
4. 透明图例，显示公式
5. 按R²从高到低排序
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.lines import Line2D
from pathlib import Path
from scipy import stats
from scipy.spatial import ConvexHull
import warnings
warnings.filterwarnings('ignore')

def setup_acm_style():
    """设置ACM论文风格"""
    plt.style.use('default')
    plt.rcParams.update({
        'font.family': 'serif',
        'font.serif': ['Times New Roman', 'Times', 'DejaVu Serif', 'Bitstream Vera Serif', 'Computer Modern Roman'],
        'font.size': 12,
        'axes.titlesize': 14,
        'axes.labelsize': 12,
        'xtick.labelsize': 10,
        'ytick.labelsize': 10,
        'legend.fontsize': 9,
        'figure.titlesize': 16,
        'lines.linewidth': 2,
        'lines.markersize': 6,
        'axes.linewidth': 1.2,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'axes.grid': True,
        'grid.linewidth': 0.8,
        'grid.alpha': 0.3,
        'figure.facecolor': 'white',
        'axes.facecolor': 'white'
    })

def get_significance_stars(p_value):
    """根据p值返回显著性星号"""
    if p_value < 0.001:
        return "***"
    elif p_value < 0.01:
        return "**"
    elif p_value < 0.05:
        return "*"
    else:
        return ""

def load_real_data():
    """加载真实的embedding reference数据并准备散点图数据"""
    # 加载包含真实embedding reference距离的数据
    df = pd.read_csv('uq_result_analysis/data/UQ_result_topic_labeling_with_reference.csv')

    # 指定的UQ方法
    selected_methods = [
        'EigValLaplacianJaccardUQ',
        'EigValLaplacianNLIUQ',
        'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ',
        'SemanticEntropyNLIUQ',
        'EmbeddingQwenUQ'
    ]

    # 过滤数据
    filtered_data = df[
        (df['llm_model'].isin(['gpt-oss:20b', 'phi4:latest'])) &
        (df['uq_method'].isin(selected_methods))
    ].copy()

    print(f"Filtered data shape: {filtered_data.shape}")
    print(f"LLM models: {filtered_data['llm_model'].unique()}")
    print(f"UQ methods: {filtered_data['uq_method'].unique()}")

    # 准备散点图数据 - 所有UQ方法与embedding reference距离的关系
    scatter_data = []

    # 首先获取每个document的embedding reference距离
    embedding_distances = {}
    for _, row in filtered_data.iterrows():
        if row['uq_method'] in ['EmbeddingQwenUQ', 'EmbeddingE5UQ']:
            key = (row['document_id'], row['llm_model'])
            if key not in embedding_distances:
                embedding_distances[key] = {}

            if row['uq_method'] == 'EmbeddingQwenUQ':
                embedding_distances[key]['Qwen'] = row['avg_distance_to_reference']
            elif row['uq_method'] == 'EmbeddingE5UQ':
                embedding_distances[key]['E5'] = row['avg_distance_to_reference']

    # 然后为所有UQ方法创建散点数据
    for _, row in filtered_data.iterrows():
        uq_method = row['uq_method']
        uq_value = row['uq_value']
        llm_model = row['llm_model']
        document_id = row['document_id']

        key = (document_id, llm_model)

        if key in embedding_distances:
            # 为每个embedding模型创建数据点
            for embedding_model in ['E5', 'Qwen']:
                if embedding_model in embedding_distances[key]:
                    reference_distance = embedding_distances[key][embedding_model]

                    if pd.notna(uq_value) and pd.notna(reference_distance):
                        scatter_data.append({
                            'llm_model': llm_model,
                            'uq_method': uq_method,
                            'embedding_model': embedding_model,
                            'uq_value': uq_value,
                            'reference_distance': reference_distance,
                            'document_id': document_id
                        })

    scatter_df = pd.DataFrame(scatter_data)
    print(f"Scatter data shape: {scatter_df.shape}")

    return scatter_df

def compute_fitting_results(scatter_data):
    """基于真实散点数据计算拟合结果 - 直接拟合 uq_value ~ reference_distance"""
    from scipy import stats

    fitting_results = []

    # 按模型组合和方法分组
    for (llm_model, embedding_model), group in scatter_data.groupby(['llm_model', 'embedding_model']):
        for uq_method in group['uq_method'].unique():
            method_data = group[group['uq_method'] == uq_method]

            if len(method_data) < 2:
                continue

            # 直接按显示的坐标方向拟合：x=reference_distance, y=uq_value
            x_data = method_data['reference_distance'].values
            y_data = method_data['uq_value'].values

            # 计算线性拟合：uq_value = slope * reference_distance + intercept
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_data, y_data)
            r_squared = r_value * r_value

            fitting_results.append({
                'llm_model': llm_model,
                'embedding_model': embedding_model,
                'uq_method': uq_method,
                'coefficient': slope,  # 这是 duq_value/dreference_distance
                'intercept': intercept,
                'r_squared': r_squared,
                'p_value': p_value,
                'std_err': std_err,
                'n_samples': len(method_data)
            })

    return pd.DataFrame(fitting_results)

def generate_scatter_data(original_data, fitting_data):
    """基于拟合结果生成散点数据"""
    scatter_data = []
    
    for _, fit_row in fitting_data.iterrows():
        llm_model = fit_row['llm_model']
        uq_method = fit_row['uq_method']
        embedding_model = fit_row['embedding_model']
        
        # 从原始数据中获取该方法的UQ值
        orig_subset = original_data[
            (original_data['llm_model'] == llm_model) & 
            (original_data['uq_method'] == uq_method)
        ]
        
        if len(orig_subset) == 0:
            continue
            
        # 获取原始UQ值（不归一化）
        uq_values = orig_subset['uq_value'].dropna()
        if len(uq_values) == 0:
            continue
            
        # 使用拟合参数生成对应的reference距离
        coefficient = fit_row['coefficient']
        intercept = fit_row['intercept']
        r_squared = fit_row['r_squared']
        
        # 计算拟合的y值
        y_fitted = coefficient * uq_values + intercept
        
        # 添加噪声来模拟真实散点
        noise_std = np.std(y_fitted) * np.sqrt(1 - r_squared) * 0.3
        y_data = y_fitted + np.random.normal(0, noise_std, len(uq_values))
        
        # 确保y值在合理范围内
        y_data = np.clip(y_data, 0, 1)
        
        for x, y in zip(uq_values, y_data):
            scatter_data.append({
                'llm_model': llm_model,
                'uq_method': uq_method,
                'embedding_model': embedding_model,
                'uq_value': x,
                'reference_distance': y,
                'r_squared': r_squared,
                'coefficient': coefficient,
                'intercept': intercept,
                'p_value': fit_row['p_value']
            })
    
    return pd.DataFrame(scatter_data)

def create_individual_method_plots(scatter_data, fitting_data, output_dir):
    """为每个模型组合下的每个UQ方法单独绘制一张图（散点+拟合线）"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}

    plot_count = 0

    for llm_model in llm_models:
        for embedding_model in embedding_models:
            # 过滤当前模型组合的数据
            model_subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if model_subset.empty:
                print(f"No data for {llm_model} + {embedding_model}")
                continue

            # 为每个UQ方法创建单独的图
            for method in selected_methods:
                method_data = model_subset[model_subset['uq_method'] == method]

                if len(method_data) == 0:
                    print(f"No data for {method} in {llm_model} + {embedding_model}")
                    continue

                # 创建单独的图表
                fig, ax = plt.subplots(1, 1, figsize=(10, 8))

                style = method_styles[method]

                # 绘制散点 - 交换坐标
                ax.scatter(method_data['reference_distance'], method_data['uq_value'],
                          c=style['color'], marker=style['marker'], s=80, alpha=0.7,
                          edgecolors='black', linewidth=0.8,
                          label=f'{method.replace("UQ", "")} Data Points')

                # 绘制拟合线 - 直接使用正确的拟合方向
                x_min, x_max = method_data['reference_distance'].min(), method_data['reference_distance'].max()
                x_range = np.linspace(x_min, x_max, 100)

                # 从fitting_data中获取拟合参数
                fit_info = fitting_data[
                    (fitting_data['llm_model'] == llm_model) &
                    (fitting_data['embedding_model'] == embedding_model) &
                    (fitting_data['uq_method'] == method)
                ]

                if len(fit_info) == 0:
                    continue

                # 直接使用拟合系数：uq_value = coef * reference_distance + intercept
                coef = fit_info['coefficient'].iloc[0]
                intercept = fit_info['intercept'].iloc[0]

                y_fitted = coef * x_range + intercept

                ax.plot(x_range, y_fitted, color=style['color'], linewidth=4, alpha=0.9,
                       label='Fitting Line')

                # 添加统计信息
                r2 = fit_info['r_squared'].iloc[0]
                p_val = fit_info['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if intercept >= 0:
                    formula = f'y = {coef:.3f}x + {intercept:.3f}'
                else:
                    formula = f'y = {coef:.3f}x - {abs(intercept):.3f}'

                # 设置图表属性
                ax.set_xlabel('Embedding Reference Distance', fontweight='bold', fontsize=12)
                ax.set_ylabel('UQ Value (Original Scale)', fontweight='bold', fontsize=12)
                ax.set_title(f'{method.replace("UQ", "")} Method\n{llm_model} + {embedding_model}',
                            fontweight='bold', pad=20, fontsize=14)
                ax.grid(True, alpha=0.3)

                # 添加图例
                ax.legend(loc='best', frameon=True, framealpha=0.8, fontsize=11)

                # 添加统计信息文本框
                stats_text = f'{formula}\nR² = {r2:.3f}{stars}\nSamples: {len(method_data)}'
                ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                       verticalalignment='top', fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9,
                               edgecolor=style['color'], linewidth=2))

                plt.tight_layout()

                # 保存图表
                safe_llm = llm_model.replace(':', '_')
                safe_method = method.replace('UQ', '')
                filename = f'individual_{safe_method}_{safe_llm}_{embedding_model}.pdf'
                output_path = output_dir / filename
                plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)

                png_filename = f'individual_{safe_method}_{safe_llm}_{embedding_model}.png'
                png_path = output_dir / png_filename
                plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)

                plt.close()
                plot_count += 1

                print(f"Individual plot saved: {safe_method} - {llm_model} + {embedding_model}")

    print(f"Total individual plots created: {plot_count}")

def create_convex_hull(x_data, y_data, alpha=0.3):
    """创建凸包来框定散点区域"""
    if len(x_data) < 3:
        return None, None
    
    points = np.column_stack([x_data, y_data])
    try:
        hull = ConvexHull(points)
        hull_points = points[hull.vertices]
        # 闭合多边形
        hull_points = np.vstack([hull_points, hull_points[0]])
        return hull_points[:, 0], hull_points[:, 1]
    except:
        return None, None

def create_fitting_line_only_plot(scatter_data, fitting_data, output_dir):
    """创建纯拟合线图 - 只显示拟合线，不显示散点"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Topic Labeling: UQ Methods - Fitting Lines Only',
                fontsize=16, fontweight='bold', y=0.95)

    # 定义颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    method_colors = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_colors[method] = colors[i]

    for i, llm_model in enumerate(llm_models):
        for j, embedding_model in enumerate(embedding_models):
            ax = axes[i, j]

            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                ax.text(0.5, 0.5, 'No Data', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{llm_model} + {embedding_model}')
                continue

            # 获取当前模型组合的拟合结果并按R²排序
            fitting_subset = fitting_data[
                (fitting_data['llm_model'] == llm_model) &
                (fitting_data['embedding_model'] == embedding_model)
            ]
            method_r2 = fitting_subset.set_index('uq_method')['r_squared'].sort_values(ascending=False)
            formula_texts = []

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                # 获取数据范围
                x_min, x_max = method_data['uq_value'].min(), method_data['uq_value'].max()
                x_range = np.linspace(x_min, x_max, 100)

                # 交换坐标后，需要重新计算拟合线
                # 原来是 y = coef * x + intercept (reference_distance = coef * uq_value + intercept)
                # 现在要 x = reference_distance, y = uq_value
                # 所以需要求反函数: uq_value = (reference_distance - intercept) / coef
                coef = method_data['coefficient'].iloc[0]
                intercept = method_data['intercept'].iloc[0]

                # 计算反函数的系数
                if abs(coef) > 1e-10:  # 避免除零
                    new_coef = 1.0 / coef
                    new_intercept = -intercept / coef
                else:
                    new_coef = 0
                    new_intercept = 0

                y_fitted = new_coef * x_range + new_intercept

                ax.plot(x_range, y_fitted, color=method_colors[method],
                       linewidth=3, alpha=0.9, label=method.replace('UQ', ''))

                # 准备公式文本 - 现在是 y = new_coef * x + new_intercept
                r2 = method_data['r_squared'].iloc[0]
                p_val = method_data['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if new_intercept >= 0:
                    formula = f'y = {new_coef:.3f}x + {new_intercept:.3f}'
                else:
                    formula = f'y = {new_coef:.3f}x - {abs(new_intercept):.3f}'

                formula_texts.append(f'{method.replace("UQ", "")}: {formula}, R² = {r2:.3f}{stars}')

            ax.set_xlabel('Embedding Reference Distance')
            ax.set_ylabel('UQ Value (Original Scale)')
            ax.set_title(f'{llm_model} + {embedding_model}')
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper left', frameon=True, framealpha=0.8, fontsize=9)

            # 添加公式信息
            if formula_texts:
                formula_text = '\n'.join(formula_texts)
                ax.text(0.02, 0.02, formula_text, transform=ax.transAxes,
                       verticalalignment='bottom', fontsize=7,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9))

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)

    # 保存图表
    output_path = output_dir / 'topic_labeling_fitting_lines_only.pdf'
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Fitting lines only plot saved to: {output_path}")

    png_path = output_dir / 'topic_labeling_fitting_lines_only.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
    print(f"Fitting lines only plot (PNG) saved to: {png_path}")

    plt.close()

def create_scatter_only_plot(scatter_data, fitting_data, output_dir):
    """创建纯散点图 - 只显示散点，不显示拟合线"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('Topic Labeling: UQ Methods - Scatter Points Only',
                fontsize=16, fontweight='bold', y=0.95)

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}

    for i, llm_model in enumerate(llm_models):
        for j, embedding_model in enumerate(embedding_models):
            ax = axes[i, j]

            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                ax.text(0.5, 0.5, 'No Data', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{llm_model} + {embedding_model}')
                continue

            # 按R²排序
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                style = method_styles[method]

                # 只绘制散点 - 交换x和y坐标
                ax.scatter(method_data['reference_distance'], method_data['uq_value'],
                          c=style['color'], marker=style['marker'], s=50, alpha=0.7,
                          edgecolors='black', linewidth=0.5,
                          label=method.replace('UQ', ''))

            ax.set_xlabel('Embedding Reference Distance')
            ax.set_ylabel('UQ Value (Original Scale)')
            ax.set_title(f'{llm_model} + {embedding_model}')
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper left', frameon=True, framealpha=0.8, fontsize=9)

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)

    # 保存图表
    output_path = output_dir / 'topic_labeling_scatter_only.pdf'
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Scatter only plot saved to: {output_path}")

    png_path = output_dir / 'topic_labeling_scatter_only.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
    print(f"Scatter only plot (PNG) saved to: {png_path}")

    plt.close()

def create_combined_plot(scatter_data, fitting_data, output_dir):
    """创建合并图 - 散点+拟合线+凸包的完整版本"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 创建2x2子图
    fig, axes = plt.subplots(2, 2, figsize=(18, 14))
    fig.suptitle('Topic Labeling: UQ Methods vs Embedding Reference Distance',
                fontsize=16, fontweight='bold', y=0.95)

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ',
        'EigValLaplacianNLIUQ',
        'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ',
        'SemanticEntropyNLIUQ',
        'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}
    
    for i, llm_model in enumerate(llm_models):
        for j, embedding_model in enumerate(embedding_models):
            ax = axes[i, j]

            # 过滤当前子图的数据
            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                ax.text(0.5, 0.5, 'No Data', ha='center', va='center',
                       transform=ax.transAxes, fontsize=14)
                ax.set_title(f'{llm_model} + {embedding_model}')
                continue

            # 按R²排序方法
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)

            # 第一步：绘制所有散点，确定数据范围
            all_x_data = []
            all_y_data = []
            method_data_dict = {}

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                method_data_dict[method] = method_data
                # 交换坐标后：x是reference_distance，y是uq_value
                all_x_data.extend(method_data['reference_distance'].values)
                all_y_data.extend(method_data['uq_value'].values)

                style = method_styles[method]

                # 绘制散点 - 交换x和y坐标
                ax.scatter(method_data['reference_distance'], method_data['uq_value'],
                          c=style['color'], marker=style['marker'], s=40, alpha=0.7,
                          edgecolors='black', linewidth=0.5,
                          label=method.replace('UQ', ''))

            # 第二步：基于散点数据范围设置坐标轴
            if all_x_data and all_y_data:
                x_min, x_max = min(all_x_data), max(all_x_data)
                y_min, y_max = min(all_y_data), max(all_y_data)

                # 添加一些边距
                x_margin = (x_max - x_min) * 0.05
                y_margin = (y_max - y_min) * 0.05

                ax.set_xlim(x_min - x_margin, x_max + x_margin)
                ax.set_ylim(y_min - y_margin, y_max + y_margin)

            # 第三步：在确定的范围内绘制拟合线和凸包
            formula_texts = []

            for method in method_r2.index:
                if method not in method_data_dict:
                    continue

                method_data = method_data_dict[method]
                style = method_styles[method]

                # 绘制凸包 - 交换坐标
                hull_x, hull_y = create_convex_hull(method_data['reference_distance'], method_data['uq_value'])
                if hull_x is not None and hull_y is not None:
                    ax.fill(hull_x, hull_y, color=style['color'], alpha=0.1)
                    ax.plot(hull_x, hull_y, color=style['color'], alpha=0.5, linewidth=2, linestyle='--')

                # 绘制拟合线 - 交换坐标后重新计算
                # 现在x是reference_distance，y是uq_value
                method_x_min = method_data['reference_distance'].min()
                method_x_max = method_data['reference_distance'].max()
                x_range = np.linspace(method_x_min, method_x_max, 50)

                # 原来的拟合是 reference_distance = coef * uq_value + intercept
                # 现在要 uq_value = f(reference_distance)，需要求反函数
                coef = method_data['coefficient'].iloc[0]
                intercept = method_data['intercept'].iloc[0]

                if abs(coef) > 1e-10:  # 避免除零
                    new_coef = 1.0 / coef
                    new_intercept = -intercept / coef
                else:
                    new_coef = 0
                    new_intercept = 0

                y_fitted = new_coef * x_range + new_intercept

                ax.plot(x_range, y_fitted, color=style['color'], linewidth=3, alpha=0.9)

                # 准备公式文本
                r2 = method_data['r_squared'].iloc[0]
                p_val = method_data['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if intercept >= 0:
                    formula = f'y = {coef:.3f}x + {intercept:.3f}'
                else:
                    formula = f'y = {coef:.3f}x - {abs(intercept):.3f}'

                formula_text = f'{method.replace("UQ", "")}: {formula}, R² = {r2:.3f}{stars}'
                formula_texts.append(formula_text)

            # 第四步：设置图表属性和图例 - 交换坐标轴标签
            ax.set_xlabel('Embedding Reference Distance')
            ax.set_ylabel('UQ Value (Original Scale)')
            ax.set_title(f'{llm_model} + {embedding_model}')
            ax.grid(True, alpha=0.3)

            # 分离的图例设计：方法标识 + 公式信息
            if method_data_dict:
                # 方法标识图例（简洁）
                method_legend = ax.legend(loc='upper left', frameon=True, framealpha=0.8,
                                        fontsize=9, title='Methods')

                # 公式信息（文本框，放在不遮挡数据的位置）
                if formula_texts:
                    formula_text = '\n'.join(formula_texts)
                    ax.text(0.02, 0.02, formula_text, transform=ax.transAxes,
                           verticalalignment='bottom', fontsize=7,
                           bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.9))

                # 确保方法图例不被覆盖
                ax.add_artist(method_legend)
    
    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    
    # 保存图表
    output_path = output_dir / 'topic_labeling_scatter_combined.pdf'
    plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
    print(f"Combined scatter plot saved to: {output_path}")
    
    png_path = output_dir / 'topic_labeling_scatter_combined.png'
    plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
    print(f"Combined scatter plot (PNG) saved to: {png_path}")
    
    plt.close()

def create_separate_fitting_only_plots(scatter_data, fitting_data, output_dir):
    """创建分离的纯拟合线图"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 定义颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    method_colors = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_colors[method] = colors[i]

    for llm_model in llm_models:
        for embedding_model in embedding_models:
            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                print(f"No data for {llm_model} + {embedding_model}")
                continue

            # 创建单独的图表
            fig, ax = plt.subplots(1, 1, figsize=(12, 9))

            # 按R²排序
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)
            formula_texts = []

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                # 获取数据范围并绘制拟合线
                x_min, x_max = method_data['uq_value'].min(), method_data['uq_value'].max()
                x_range = np.linspace(x_min, x_max, 100)

                coef = method_data['coefficient'].iloc[0]
                intercept = method_data['intercept'].iloc[0]
                y_fitted = coef * x_range + intercept

                ax.plot(x_range, y_fitted, color=method_colors[method],
                       linewidth=4, alpha=0.9, label=method.replace('UQ', ''))

                # 准备公式文本
                r2 = method_data['r_squared'].iloc[0]
                p_val = method_data['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if intercept >= 0:
                    formula = f'y = {coef:.3f}x + {intercept:.3f}'
                else:
                    formula = f'y = {coef:.3f}x - {abs(intercept):.3f}'

                formula_texts.append(f'{method.replace("UQ", "")}: {formula}, R² = {r2:.3f}{stars}')

            ax.set_xlabel('UQ Value (Original Scale)', fontweight='bold')
            ax.set_ylabel('Embedding Reference Distance', fontweight='bold')
            ax.set_title(f'Fitting Lines Only: {llm_model} + {embedding_model}',
                        fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper left', frameon=True, framealpha=0.8, fontsize=10)

            # 添加公式信息
            if formula_texts:
                formula_text = '\n'.join(formula_texts)
                ax.text(0.02, 0.02, formula_text, transform=ax.transAxes,
                       verticalalignment='bottom', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))

            plt.tight_layout()

            # 保存图表
            safe_llm = llm_model.replace(':', '_')
            filename = f'topic_labeling_fitting_only_{safe_llm}_{embedding_model}.pdf'
            output_path = output_dir / filename
            plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
            print(f"Separate fitting only plot saved to: {output_path}")

            png_filename = f'topic_labeling_fitting_only_{safe_llm}_{embedding_model}.png'
            png_path = output_dir / png_filename
            plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
            print(f"Separate fitting only plot (PNG) saved to: {png_path}")

            plt.close()

def create_separate_scatter_only_plots(scatter_data, fitting_data, output_dir):
    """创建分离的纯散点图"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}

    for llm_model in llm_models:
        for embedding_model in embedding_models:
            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                print(f"No data for {llm_model} + {embedding_model}")
                continue

            # 创建单独的图表
            fig, ax = plt.subplots(1, 1, figsize=(12, 9))

            # 按R²排序方法
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                style = method_styles[method]

                # 只绘制散点
                ax.scatter(method_data['uq_value'], method_data['reference_distance'],
                          c=style['color'], marker=style['marker'], s=60, alpha=0.7,
                          edgecolors='black', linewidth=0.5,
                          label=method.replace('UQ', ''))

            ax.set_xlabel('UQ Value (Original Scale)', fontweight='bold')
            ax.set_ylabel('Embedding Reference Distance', fontweight='bold')
            ax.set_title(f'Scatter Points Only: {llm_model} + {embedding_model}',
                        fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)
            ax.legend(loc='upper left', frameon=True, framealpha=0.8, fontsize=10)

            plt.tight_layout()

            # 保存图表
            safe_llm = llm_model.replace(':', '_')
            filename = f'topic_labeling_scatter_only_{safe_llm}_{embedding_model}.pdf'
            output_path = output_dir / filename
            plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
            print(f"Separate scatter only plot saved to: {output_path}")

            png_filename = f'topic_labeling_scatter_only_{safe_llm}_{embedding_model}.png'
            png_path = output_dir / png_filename
            plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
            print(f"Separate scatter only plot (PNG) saved to: {png_path}")

            plt.close()

def create_separate_combined_plots(scatter_data, fitting_data, output_dir):
    """创建分离的合并图（散点+拟合线+凸包）"""
    setup_acm_style()

    llm_models = ['gpt-oss:20b', 'phi4:latest']
    embedding_models = ['E5', 'Qwen']

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ', 'EigValLaplacianNLIUQ', 'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ', 'SemanticEntropyNLIUQ', 'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}

    for llm_model in llm_models:
        for embedding_model in embedding_models:
            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                print(f"No data for {llm_model} + {embedding_model}")
                continue

            # 创建单独的图表
            fig, ax = plt.subplots(1, 1, figsize=(12, 9))

            # 按R²排序方法
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)
            formula_texts = []

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]
                if len(method_data) == 0:
                    continue

                style = method_styles[method]

                # 绘制散点
                ax.scatter(method_data['uq_value'], method_data['reference_distance'],
                          c=style['color'], marker=style['marker'], s=60, alpha=0.7,
                          edgecolors='black', linewidth=0.5,
                          label=method.replace('UQ', ''))

                # 绘制凸包
                hull_x, hull_y = create_convex_hull(method_data['uq_value'], method_data['reference_distance'])
                if hull_x is not None and hull_y is not None:
                    ax.fill(hull_x, hull_y, color=style['color'], alpha=0.1)
                    ax.plot(hull_x, hull_y, color=style['color'], alpha=0.5, linewidth=2, linestyle='--')

                # 绘制拟合线
                x_min, x_max = method_data['uq_value'].min(), method_data['uq_value'].max()
                x_range = np.linspace(x_min, x_max, 100)
                coef = method_data['coefficient'].iloc[0]
                intercept = method_data['intercept'].iloc[0]
                y_fitted = coef * x_range + intercept
                ax.plot(x_range, y_fitted, color=style['color'], linewidth=4, alpha=0.9)

                # 准备公式文本
                r2 = method_data['r_squared'].iloc[0]
                p_val = method_data['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if intercept >= 0:
                    formula = f'y = {coef:.3f}x + {intercept:.3f}'
                else:
                    formula = f'y = {coef:.3f}x - {abs(intercept):.3f}'

                formula_texts.append(f'{method.replace("UQ", "")}: {formula}, R² = {r2:.3f}{stars}')

            ax.set_xlabel('UQ Value (Original Scale)', fontweight='bold')
            ax.set_ylabel('Embedding Reference Distance', fontweight='bold')
            ax.set_title(f'Combined Plot: {llm_model} + {embedding_model}',
                        fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)

            # 分离的图例设计
            method_legend = ax.legend(loc='upper left', frameon=True, framealpha=0.8,
                                    fontsize=10, title='Methods')

            # 公式信息文本框
            if formula_texts:
                formula_text = '\n'.join(formula_texts)
                ax.text(0.02, 0.02, formula_text, transform=ax.transAxes,
                       verticalalignment='bottom', fontsize=8,
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9))

            ax.add_artist(method_legend)
            plt.tight_layout()

            # 保存图表
            safe_llm = llm_model.replace(':', '_')
            filename = f'topic_labeling_combined_{safe_llm}_{embedding_model}.pdf'
            output_path = output_dir / filename
            plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
            print(f"Separate combined plot saved to: {output_path}")

            png_filename = f'topic_labeling_combined_{safe_llm}_{embedding_model}.png'
            png_path = output_dir / png_filename
            plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
            print(f"Separate combined plot (PNG) saved to: {png_path}")

            plt.close()

    # 定义颜色和marker
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    markers = ['o', 's', '^', 'D', 'v', 'p']

    method_styles = {}
    selected_methods = [
        'EigValLaplacianJaccardUQ',
        'EigValLaplacianNLIUQ',
        'EccentricityJaccardUQ',
        'EccentricityNLIEntailUQ',
        'SemanticEntropyNLIUQ',
        'EmbeddingQwenUQ'
    ]

    for i, method in enumerate(selected_methods):
        method_styles[method] = {'color': colors[i], 'marker': markers[i]}

    for llm_model in llm_models:
        for embedding_model in embedding_models:
            # 过滤数据
            subset = scatter_data[
                (scatter_data['llm_model'] == llm_model) &
                (scatter_data['embedding_model'] == embedding_model)
            ]

            if subset.empty:
                print(f"No data for {llm_model} + {embedding_model}")
                continue

            # 创建单独的图表
            fig, ax = plt.subplots(1, 1, figsize=(12, 9))

            # 按R²排序方法
            method_r2 = subset.groupby('uq_method')['r_squared'].first().sort_values(ascending=False)

            legend_elements = []

            for method in method_r2.index:
                method_data = subset[subset['uq_method'] == method]

                if len(method_data) == 0:
                    continue

                style = method_styles[method]

                # 绘制散点
                ax.scatter(method_data['uq_value'], method_data['reference_distance'],
                          c=style['color'], marker=style['marker'], s=50, alpha=0.6,
                          edgecolors='black', linewidth=0.5, label=method.replace('UQ', ''))

                # 绘制凸包
                hull_x, hull_y = create_convex_hull(method_data['uq_value'], method_data['reference_distance'])
                if hull_x is not None and hull_y is not None:
                    ax.fill(hull_x, hull_y, color=style['color'], alpha=0.1)
                    ax.plot(hull_x, hull_y, color=style['color'], alpha=0.5, linewidth=2, linestyle='--')

                # 绘制拟合线 - 基于散点的实际数据范围
                x_min, x_max = method_data['uq_value'].min(), method_data['uq_value'].max()
                x_range = np.linspace(x_min, x_max, 100)
                coef = method_data['coefficient'].iloc[0]
                intercept = method_data['intercept'].iloc[0]
                y_fitted = coef * x_range + intercept
                ax.plot(x_range, y_fitted, color=style['color'], linewidth=3, alpha=0.8)

                # 准备图例信息
                r2 = method_data['r_squared'].iloc[0]
                p_val = method_data['p_value'].iloc[0]
                stars = get_significance_stars(p_val)

                if intercept >= 0:
                    formula = f'y = {coef:.3f}x + {intercept:.3f}'
                else:
                    formula = f'y = {coef:.3f}x - {abs(intercept):.3f}'

                formula_label = f'{formula}, R² = {r2:.3f}{stars}'
                legend_elements.append(formula_label)

            # 设置图表属性
            ax.set_xlabel('UQ Value (Original Scale)', fontweight='bold')
            ax.set_ylabel('Embedding Reference Distance', fontweight='bold')
            ax.set_title(f'Topic Labeling: {llm_model} + {embedding_model}',
                        fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)

            # 添加两个图例：一个用于方法标识，一个用于公式
            if legend_elements:
                # 方法标识图例
                method_legend = ax.legend(loc='upper left', frameon=True, framealpha=0.8,
                                        title='UQ Methods', title_fontsize=10)

                # 公式图例（作为文本框）
                formula_text = '\n'.join(legend_elements)
                ax.text(0.02, 0.98, formula_text, transform=ax.transAxes,
                       verticalalignment='top', fontsize=8,
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8))

                # 确保方法图例不被覆盖
                ax.add_artist(method_legend)

            plt.tight_layout()

            # 保存图表
            safe_llm = llm_model.replace(':', '_')
            filename = f'topic_labeling_scatter_{safe_llm}_{embedding_model}.pdf'
            output_path = output_dir / filename
            plt.savefig(output_path, format='pdf', bbox_inches='tight', dpi=300)
            print(f"Separate scatter plot saved to: {output_path}")

            # PNG格式
            png_filename = f'topic_labeling_scatter_{safe_llm}_{embedding_model}.png'
            png_path = output_dir / png_filename
            plt.savefig(png_path, format='png', bbox_inches='tight', dpi=300)
            print(f"Separate scatter plot (PNG) saved to: {png_path}")

            plt.close()

def main():
    """主函数"""
    print("=== Topic Labeling Scatter Plots ===")
    
    # 创建输出目录
    output_dir = Path('uq_result_analysis/figures/topic_labeling_scatter')
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 加载真实数据
    scatter_data = load_real_data()

    if scatter_data.empty:
        print("No scatter data generated")
        return

    print(f"Generated {len(scatter_data)} scatter points")

    # 基于真实数据计算拟合结果
    print("\nComputing fitting results from real data...")
    fitting_data = compute_fitting_results(scatter_data)
    print(f"Computed fitting results for {len(fitting_data)} method combinations")

    # 创建个别方法图（这个已经修正好了）
    print("\n4. Creating individual method plots...")
    create_individual_method_plots(scatter_data, fitting_data, output_dir)

    # 暂时跳过其他图表，先确保个别方法图能工作
    # print("\n1. Creating fitting lines only plots...")
    # create_fitting_line_only_plot(scatter_data, fitting_data, output_dir)

    # print("\n2. Creating scatter points only plots...")
    # create_scatter_only_plot(scatter_data, fitting_data, output_dir)

    # print("\n3. Creating combined plots (scatter + fitting + hull)...")
    # create_combined_plot(scatter_data, fitting_data, output_dir)

    print(f"\nAll plots saved to: {output_dir.absolute()}")
    print("\nGenerated files:")
    for file in sorted(output_dir.glob('*')):
        size_kb = file.stat().st_size / 1024
        print(f"  - {file.name} ({size_kb:.0f}KB)")

if __name__ == "__main__":
    main()
