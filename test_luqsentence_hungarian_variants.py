#!/usr/bin/env python3
"""
测试LUQSENTENCE Hungarian变体的效果
This script tests the new Hungarian algorithm-based sentence matching variants.
"""

import sys
import os
import logging
import numpy as np
from typing import List, Dict, Any
from pymongo import MongoClient

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from uq_methods.implementations.luq_sentence import (
    LUQSENTENCEUQ, 
    LUQSENTENCEHungarianUQ, 
    create_luqsentence_hungarian_variants,
    sentence_matching,
    sentence_matching_hungarian
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
log = logging.getLogger(__name__)


def get_test_counterfactual_sample(limit: int = 1) -> List[Dict[str, Any]]:
    """
    从MongoDB获取一个counterfactual测试样本
    
    Args:
        limit: 获取的样本数量
        
    Returns:
        List of sample dictionaries with responses and metadata
    """
    try:
        client = MongoClient("localhost", 27017)
        db = client["LLM-UQ"]
        collection = db["response_collections"]
        
        pipeline = [
            {"$match": {"task_name": "counterfactual_qa", "dataset_source": "counterfactual_data"}},
            {"$group": {
                "_id": {"category": "$category", "row_index": "$row_index", "prompt_seed": "$prompt_seed"},
                "responses": {"$push": "$parsed_answer"},
                "count": {"$sum": 1},
                "sample_doc": {"$first": "$$ROOT"}  # 保留一个文档用于获取元数据
            }},
            {"$match": {"count": {"$gte": 3}}},  # 至少3个响应
            {"$limit": limit}
        ]
        
        result = list(collection.aggregate(pipeline))
        client.close()
        
        samples = []
        for group in result:
            sample = {
                "category": group["_id"]["category"],
                "row_index": group["_id"]["row_index"],
                "prompt_seed": group["_id"]["prompt_seed"],
                "responses": group["responses"][:5],  # 最多取5个响应
                "count": len(group["responses"][:5]),
                "prompt_text": group["sample_doc"].get("prompt_text", ""),
                "task_id": group["sample_doc"].get("task_id", "")
            }
            samples.append(sample)
            
        log.info(f"获取到 {len(samples)} 个counterfactual测试样本")
        return samples
        
    except Exception as e:
        log.error(f"获取测试样本失败: {e}")
        return []


def compare_matching_algorithms(sample: Dict[str, Any]):
    """
    比较贪心匹配算法和匈牙利算法的句子匹配效果
    """
    responses = sample["responses"][:2]  # 只取前两个响应进行比较
    category = sample["category"]
    row_index = sample["row_index"]

    log.info(f"\n{'='*80}")
    log.info(f"匹配算法比较: {category} - 问题 {row_index}")
    log.info(f"{'='*80}")

    # 初始化LUQSENTENCE用于句子分割
    luq_sentence = LUQSENTENCEUQ(verbose=False)
    
    sentences_a = luq_sentence._split_into_sentences(responses[0])
    sentences_b = luq_sentence._split_into_sentences(responses[1])

    log.info(f"\n响应A句子数: {len(sentences_a)}")
    log.info(f"响应B句子数: {len(sentences_b)}")

    # 测试配置
    test_configs = [
        {"pct_k": 0.3, "mode": "bottom"},
        {"pct_k": 0.5, "mode": "bottom"},
        {"pct_k": 0.7, "mode": "bottom"},
        {"pct_k": 0.5, "mode": "top"},
        {"pct_k": 0.5, "mode": "random"},
    ]

    for config in test_configs:
        pct_k = config["pct_k"]
        mode = config["mode"]
        
        log.info(f"\n{'-'*60}")
        log.info(f"测试配置: pct_k={pct_k}, mode={mode}")
        log.info(f"{'-'*60}")

        try:
            # 贪心算法匹配
            greedy_matches_a, greedy_matches_b = sentence_matching(
                sentences_a, sentences_b, pct_k, mode
            )

            # 匈牙利算法匹配
            hungarian_matches_a, hungarian_matches_b = sentence_matching_hungarian(
                sentences_a, sentences_b, pct_k, mode
            )

            log.info(f"贪心算法匹配到: {len(greedy_matches_a)} 对句子")
            log.info(f"匈牙利算法匹配到: {len(hungarian_matches_a)} 对句子")

            # 计算相似度差异
            if len(greedy_matches_a) > 0 and len(hungarian_matches_a) > 0:
                # 获取embedding编码器来计算实际相似度
                from core.embedding_cache import get_embedding_encoder
                encoder = get_embedding_encoder("intfloat/multilingual-e5-large-instruct")

                def calculate_avg_similarity(matches_a, matches_b):
                    similarities = []
                    for sent_a, sent_b in zip(matches_a, matches_b):
                        emb_a = encoder.encode_one(sent_a, normalize=True)
                        emb_b = encoder.encode_one(sent_b, normalize=True)
                        similarity = np.dot(emb_a, emb_b)
                        similarities.append(similarity)
                    return np.mean(similarities) if similarities else 0.0

                greedy_avg_sim = calculate_avg_similarity(greedy_matches_a, greedy_matches_b)
                hungarian_avg_sim = calculate_avg_similarity(hungarian_matches_a, hungarian_matches_b)

                log.info(f"贪心算法平均相似度: {greedy_avg_sim:.4f}")
                log.info(f"匈牙利算法平均相似度: {hungarian_avg_sim:.4f}")
                log.info(f"相似度提升: {hungarian_avg_sim - greedy_avg_sim:+.4f}")

                # 显示一些匹配例子
                log.info(f"\n匹配示例 (前3对):")
                for i in range(min(3, len(greedy_matches_a), len(hungarian_matches_a))):
                    log.info(f"\n匹配对 {i+1}:")
                    log.info(f"  贪心算法:")
                    log.info(f"    A: {greedy_matches_a[i][:100]}...")
                    log.info(f"    B: {greedy_matches_b[i][:100]}...")
                    log.info(f"  匈牙利算法:")
                    log.info(f"    A: {hungarian_matches_a[i][:100]}...")
                    log.info(f"    B: {hungarian_matches_b[i][:100]}...")

        except Exception as e:
            log.error(f"测试配置 {config} 失败: {e}")


def test_luqsentence_variants(sample: Dict[str, Any]):
    """
    测试不同的LUQSENTENCE变体
    """
    responses = sample["responses"]
    category = sample["category"]
    row_index = sample["row_index"]
    
    log.info(f"\n{'='*80}")
    log.info(f"LUQSENTENCE变体测试: {category} - 问题 {row_index}")
    log.info(f"响应数量: {len(responses)}")
    log.info(f"{'='*80}")

    # 测试原始的LUQSENTENCE
    log.info(f"\n--- 测试原始LUQSENTENCE (贪心算法) ---")
    original_luq = LUQSENTENCEUQ(pct_k=0.5, matching_mode="bottom", verbose=False)
    original_result = original_luq.compute_uncertainty(responses)
    
    log.info(f"原始LUQSENTENCE不确定性分数: {original_result.get('uncertainty_score', 'N/A'):.4f}")
    log.info(f"原始LUQSENTENCE整体一致性: {original_result.get('overall_consistency', 'N/A'):.4f}")
    log.info(f"匹配的句子对总数: {original_result.get('metadata', {}).get('total_sentence_pairs', 0)}")

    # 测试Hungarian变体
    hungarian_variants = create_luqsentence_hungarian_variants()
    
    results_comparison = []
    
    for variant_name, variant_method in hungarian_variants.items():
        log.info(f"\n--- 测试 {variant_name} ---")
        
        try:
            variant_result = variant_method.compute_uncertainty(responses)
            
            uncertainty_score = variant_result.get('uncertainty_score', 'N/A')
            consistency_score = variant_result.get('overall_consistency', 'N/A')
            total_pairs = variant_result.get('metadata', {}).get('total_sentence_pairs', 0)
            
            log.info(f"{variant_name} 不确定性分数: {uncertainty_score:.4f}")
            log.info(f"{variant_name} 整体一致性: {consistency_score:.4f}")
            log.info(f"{variant_name} 匹配句子对数: {total_pairs}")
            
            results_comparison.append({
                "name": variant_name,
                "uncertainty_score": uncertainty_score,
                "consistency_score": consistency_score,
                "total_pairs": total_pairs,
                "pct_k": variant_method.pct_k,
                "mode": variant_method.matching_mode
            })
            
        except Exception as e:
            log.error(f"{variant_name} 测试失败: {e}")

    # 结果对比分析
    log.info(f"\n{'='*80}")
    log.info("结果对比分析")
    log.info(f"{'='*80}")
    
    # 按pct_k分组分析
    pct_k_groups = {}
    for result in results_comparison:
        pct_k = result["pct_k"]
        if pct_k not in pct_k_groups:
            pct_k_groups[pct_k] = []
        pct_k_groups[pct_k].append(result)
    
    log.info(f"\n按pct_k分组分析:")
    for pct_k in sorted(pct_k_groups.keys()):
        group = pct_k_groups[pct_k]
        avg_uncertainty = np.mean([r["uncertainty_score"] for r in group])
        avg_consistency = np.mean([r["consistency_score"] for r in group])
        avg_pairs = np.mean([r["total_pairs"] for r in group])
        
        log.info(f"  pct_k={pct_k}: 平均不确定性={avg_uncertainty:.4f}, "
                f"平均一致性={avg_consistency:.4f}, 平均匹配对数={avg_pairs:.1f}")
    
    # 按matching_mode分组分析
    mode_groups = {}
    for result in results_comparison:
        mode = result["mode"]
        if mode not in mode_groups:
            mode_groups[mode] = []
        mode_groups[mode].append(result)
    
    log.info(f"\n按匹配模式分组分析:")
    for mode in ["bottom", "top", "random"]:
        if mode in mode_groups:
            group = mode_groups[mode]
            avg_uncertainty = np.mean([r["uncertainty_score"] for r in group])
            avg_consistency = np.mean([r["consistency_score"] for r in group])
            
            log.info(f"  {mode}模式: 平均不确定性={avg_uncertainty:.4f}, "
                    f"平均一致性={avg_consistency:.4f}")

    # 与原始方法的比较
    if original_result.get('uncertainty_score') is not None:
        original_uncertainty = original_result['uncertainty_score']
        original_consistency = original_result['overall_consistency']
        original_pairs = original_result.get('metadata', {}).get('total_sentence_pairs', 0)
        
        log.info(f"\n与原始贪心算法的比较:")
        log.info(f"  原始方法: 不确定性={original_uncertainty:.4f}, 一致性={original_consistency:.4f}, 匹配对数={original_pairs}")
        
        for result in results_comparison:
            uncertainty_diff = result["uncertainty_score"] - original_uncertainty
            consistency_diff = result["consistency_score"] - original_consistency
            pairs_diff = result["total_pairs"] - original_pairs
            
            log.info(f"  {result['name']}: "
                    f"Δ不确定性={uncertainty_diff:+.4f}, "
                    f"Δ一致性={consistency_diff:+.4f}, "
                    f"Δ匹配对数={pairs_diff:+.0f}")


def main():
    """主函数"""
    log.info("🧪 LUQSENTENCE Hungarian变体测试")
    log.info("测试使用匈牙利算法的句子匹配效果")
    log.info("=" * 80)
    
    # 获取测试数据
    samples = get_test_counterfactual_sample(limit=1)
    if not samples:
        log.error("❌ 无法获取测试样本")
        return 1
    
    sample = samples[0]
    log.info(f"📊 测试样本: {sample['category']} - 问题 {sample['row_index']}")
    log.info(f"响应数量: {sample['count']}")

    # 1. 比较匹配算法
    log.info(f"\n🎯 第一部分：比较贪心算法 vs 匈牙利算法")
    compare_matching_algorithms(sample)
    
    # 2. 测试LUQSENTENCE变体
    log.info(f"\n🎯 第二部分：测试LUQSENTENCE Hungarian变体")
    test_luqsentence_variants(sample)
    
    log.info(f"\n{'='*80}")
    log.info("测试完成!")
    log.info(f"{'='*80}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())