# Phi-4 OpenAI兼容API服务器

简单启动Phi-4模型作为OpenAI兼容的API服务器，在8000端口监听，使用GPU 1。

## 快速启动

### 方法1：使用bash脚本（推荐）
```bash
./start_phi4_server.sh
```

### 方法2：使用Python脚本
```bash
python3 start_phi4_server.py
```

### 方法3：指定模型路径
```bash
./start_phi4_server.sh --model /path/to/phi-4-Q4_K_M.gguf
```

## 配置选项

```bash
./start_phi4_server.sh [OPTIONS]

Options:
  -m, --model PATH     Path to Phi-4 GGUF model file
  -p, --port PORT      Port to listen on (default: 8000)
  --host HOST          Host to bind to (default: 0.0.0.0)
  --gpu-layers N       Number of GPU layers (default: 32)
  --ctx-size N         Context size (default: 4096)
  --threads N          Number of threads (default: 8)
  -h, --help           Show help message
```

## 使用示例

### 启动服务器
```bash
# 默认配置（自动查找模型，8000端口，GPU 1）
./start_phi4_server.sh

# 自定义端口
./start_phi4_server.sh --port 8001

# 自定义GPU层数（如果显存不足）
./start_phi4_server.sh --gpu-layers 16
```

### 测试API
```bash
# 测试服务器是否正常
python3 test_phi4_api.py

# 等待服务器启动后测试
python3 test_phi4_api.py --wait
```

## OpenAI格式访问

服务器启动后，你可以使用OpenAI的格式访问：

### 端点
- **Base URL**: `http://localhost:8000`
- **Chat Completions**: `http://localhost:8000/v1/chat/completions`

### Python示例
```python
import openai

# 配置客户端
client = openai.OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="not-needed"  # 本地服务器不需要API key
)

# 发送请求
response = client.chat.completions.create(
    model="phi-4",
    messages=[
        {"role": "user", "content": "Hello, how are you?"}
    ],
    max_tokens=100,
    temperature=0.7
)

print(response.choices[0].message.content)
```

### curl示例
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "phi-4",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ],
    "max_tokens": 50,
    "temperature": 0.7
  }'
```

## 自动配置

- ✅ **GPU 1**: 自动设置 `CUDA_VISIBLE_DEVICES=1`
- ✅ **模型查找**: 自动在常见位置查找模型文件
- ✅ **依赖安装**: 如果需要会自动安装 llama-cpp-python
- ✅ **CUDA支持**: 自动启用CUDA加速

## 模型文件位置

脚本会自动在以下位置查找 `phi-4-Q4_K_M.gguf`:
- `./phi-4-Q4_K_M.gguf`
- `./models/phi-4-Q4_K_M.gguf`
- `~/models/phi-4-Q4_K_M.gguf`
- `/models/phi-4-Q4_K_M.gguf`
- `/data/models/phi-4-Q4_K_M.gguf`

## 后台运行

### 使用nohup
```bash
nohup ./start_phi4_server.sh > phi4_server.log 2>&1 &
```

### 使用screen
```bash
screen -S phi4-server
./start_phi4_server.sh
# 按 Ctrl+A, D 分离会话
```

### 使用systemd（推荐生产环境）
创建服务文件 `/etc/systemd/system/phi4-server.service`:
```ini
[Unit]
Description=Phi-4 OpenAI API Server
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/your/directory
Environment=CUDA_VISIBLE_DEVICES=1
ExecStart=/path/to/your/directory/start_phi4_server.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

然后启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable phi4-server
sudo systemctl start phi4-server
```

## 故障排除

### 端口被占用
```bash
# 查看端口使用情况
lsof -i :8000

# 使用其他端口
./start_phi4_server.sh --port 8001
```

### 显存不足
```bash
# 减少GPU层数
./start_phi4_server.sh --gpu-layers 16

# 或者纯CPU运行
./start_phi4_server.sh --gpu-layers 0
```

### 检查服务状态
```bash
# 检查进程
ps aux | grep llama_cpp

# 检查端口
netstat -tlnp | grep 8000

# 测试连接
curl http://localhost:8000/health
```

就这么简单！启动后你就可以用OpenAI的格式访问你的Phi-4模型了。
