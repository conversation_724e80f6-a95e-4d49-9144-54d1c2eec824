#!/usr/bin/env python3
"""
测试小批量数据处理
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vllm_sentment_generator import VLLMSentimentGenerator
import logging
import uuid

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_small_batch():
    """测试处理少量数据"""
    
    print("Testing small batch processing...")
    
    # 初始化生成器
    try:
        generator = VLLMSentimentGenerator(
            vllm_host="http://localhost:8000",
            api_key="token-abc123",
            model_name="Qwen/Qwen3-14B-FP8"
        )
        logger.info("VLLM Sentiment Generator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize generator: {e}")
        return
    
    # 加载数据集
    dataset = generator.load_dataset_from_config()
    if not dataset:
        logger.error("Failed to load dataset")
        return
    
    print(f"Dataset loaded: {len(dataset)} records")
    
    # 只处理前2条数据进行测试
    test_data = dataset[:2]
    print(f"Testing with {len(test_data)} records")
    
    # 显示测试数据
    for i, record in enumerate(test_data):
        print(f"Record {i+1}: {record['text'][:50]}... (label: {record['label']})")
    
    # 生成测试运行ID
    run_id = str(uuid.uuid4())
    print(f"Test Run ID: {run_id}")
    
    try:
        # 处理测试数据
        final_run_id = generator.process_semeval_data_like_original(test_data, run_id)
        
        print(f"✅ Test processing completed!")
        print(f"Final Run ID: {final_run_id}")
        
        # 检查MongoDB中的结果
        if hasattr(generator, 'collection') and generator.collection is not None:
            try:
                count = generator.collection.count_documents({"run_id": final_run_id})
                print(f"Records saved to MongoDB: {count}")
                
                # 显示一个示例文档
                sample_doc = generator.collection.find_one({"run_id": final_run_id})
                if sample_doc:
                    print(f"\nSample document fields:")
                    for key in sorted(sample_doc.keys()):
                        if key == '_id':
                            continue
                        value = sample_doc[key]
                        if isinstance(value, str) and len(value) > 50:
                            print(f"  {key}: {value[:50]}...")
                        elif key == 'detailed_logprobs' and isinstance(value, list):
                            print(f"  {key}: [{len(value)} tokens]")
                        else:
                            print(f"  {key}: {value}")
                            
            except Exception as e:
                logger.warning(f"Could not access MongoDB: {e}")
        
    except Exception as e:
        logger.error(f"Error during test processing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_small_batch()
