#!/usr/bin/env python3
"""
Counterfactual Generator 使用示例
演示如何使用这个独立模块生成counterfactual问答数据
"""

import os
import sys
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from counterfactual_generator import CounterfactualGenerator


def example_basic_usage():
    """基本使用示例"""
    print("📚 基本使用示例")
    print("-" * 30)
    
    # 初始化生成器
    generator = CounterfactualGenerator()
    
    # 查看配置
    print(f"模型: {generator.config['model']['name']}")
    print(f"每个prompt尝试次数: {generator.config['generation']['attempts_per_prompt']}")
    print(f"随机选择prompt数: {generator.config['generation']['sample_prompts']}")
    
    # 查看可用的prompt
    print(f"\n可用的prompt类型 ({len(generator.prompts)} 个):")
    for i, prompt_key in enumerate(generator.prompts.keys(), 1):
        print(f"  {i:2d}. {prompt_key}")
    
    # 加载问题
    questions = generator.load_questions()
    print(f"\n加载了 {len(questions)} 个问题")
    
    # 显示前3个问题
    print("\n前3个问题:")
    for i, q in enumerate(questions[:3], 1):
        print(f"  {i}. [{q['category']}] {q['question'][:80]}...")


def example_prompt_selection():
    """Prompt选择示例"""
    print("\n🎲 Prompt选择示例")
    print("-" * 30)
    
    generator = CounterfactualGenerator()
    
    # 演示不同种子的选择结果
    print("使用不同种子选择prompt:")
    for seed in [0, 1, 2]:
        selected = generator.get_random_prompts(3, seed=seed)
        print(f"  种子 {seed}: {selected}")
    
    # 演示相同种子的一致性
    print("\n验证相同种子的一致性:")
    seed = 42
    selection1 = generator.get_random_prompts(5, seed=seed)
    selection2 = generator.get_random_prompts(5, seed=seed)
    print(f"  第一次选择: {selection1}")
    print(f"  第二次选择: {selection2}")
    print(f"  结果一致: {selection1 == selection2}")


def example_prompt_formatting():
    """Prompt格式化示例"""
    print("\n🔧 Prompt格式化示例")
    print("-" * 30)
    
    generator = CounterfactualGenerator()
    
    # 选择一个问题和prompt
    questions = generator.load_questions()
    question = questions[0]['question']
    
    print(f"原始问题: {question[:100]}...")
    print("\n不同prompt风格的格式化结果:")
    
    # 展示不同风格的prompt
    styles_to_show = ['expert_direct', 'multi_perspective', 'causal_chain']
    
    for style in styles_to_show:
        if style in generator.prompts:
            template = generator.prompts[style]
            formatted = generator.format_prompt(template, question)
            
            print(f"\n{style}:")
            print(f"  模板长度: {len(template)} 字符")
            print(f"  格式化后长度: {len(formatted)} 字符")
            print(f"  预览: {formatted[:150]}...")


def example_mock_generation():
    """模拟生成过程示例（不调用真实API）"""
    print("\n🤖 模拟生成过程示例")
    print("-" * 30)
    
    generator = CounterfactualGenerator()
    
    # 只处理前2个问题
    questions = generator.load_questions()[:2]
    
    print(f"模拟处理 {len(questions)} 个问题:")
    
    for question_idx, question in enumerate(questions):
        print(f"\n问题 {question_idx + 1}: [{question['category']}]")
        print(f"  内容: {question['question'][:80]}...")
        
        # 使用问题索引作为种子选择prompt
        selected_prompts = generator.get_random_prompts(3, seed=question_idx)
        print(f"  选择的prompt: {selected_prompts}")
        
        # 模拟每个prompt的处理
        for prompt_key in selected_prompts:
            template = generator.prompts[prompt_key]
            formatted = generator.format_prompt(template, question['question'])
            
            print(f"    {prompt_key}:")
            print(f"      格式化后长度: {len(formatted)} 字符")
            print(f"      会生成 {generator.config['generation']['attempts_per_prompt']} 个响应")


def example_csv_structure():
    """展示CSV输出结构"""
    print("\n📊 CSV输出结构示例")
    print("-" * 30)
    
    # 展示CSV字段
    csv_fields = [
        "run_id", "question_id", "question_category", "question_text", 
        "row_index", "prompt_key", "prompt_template", "final_prompt",
        "attempt", "response_content", "model", "usage_tokens", 
        "error", "timestamp", "response_length", "has_error"
    ]
    
    print("CSV文件将包含以下字段:")
    for i, field in enumerate(csv_fields, 1):
        print(f"  {i:2d}. {field}")
    
    print(f"\n总共 {len(csv_fields)} 个字段")
    print("每行代表一个生成的响应")


def main():
    """运行所有示例"""
    print("🎯 Counterfactual Generator 使用示例")
    print("=" * 50)
    
    try:
        example_basic_usage()
        example_prompt_selection()
        example_prompt_formatting()
        example_mock_generation()
        example_csv_structure()
        
        print("\n" + "=" * 50)
        print("✅ 所有示例运行完成！")
        print("\n💡 实际使用方法:")
        print("1. 设置API密钥: export OPENAI_API_KEY='your-api-key'")
        print("2. 测试运行: python run_generator.py --generate --test")
        print("3. 完整运行: python run_generator.py --generate")
        print("4. 查看输出: ls output/")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
