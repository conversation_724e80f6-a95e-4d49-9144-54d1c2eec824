#!/usr/bin/env python3
"""
Counterfactual Generator 运行脚本
独立运行counterfactual问答生成任务
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from counterfactual_generator import CounterfactualGenerator


def setup_logging(level: str = "INFO"):
    """设置日志"""
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('counterfactual_generator.log')
        ]
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Counterfactual Question Answering Generator",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_generator.py --generate                    # 使用默认配置生成
  python run_generator.py --config custom_config.yaml  # 使用自定义配置
  python run_generator.py --test                        # 测试模式（只处理2个问题）
  python run_generator.py --questions data.csv          # 使用自定义问题文件
        """
    )
    
    parser.add_argument(
        '--generate', 
        action='store_true',
        help='开始生成counterfactual响应'
    )
    
    parser.add_argument(
        '--config', 
        type=str,
        default=None,
        help='配置文件路径（默认使用config.yaml）'
    )
    
    parser.add_argument(
        '--questions',
        type=str,
        default=None,
        help='问题数据文件路径（CSV格式）'
    )
    
    parser.add_argument(
        '--test',
        action='store_true',
        help='测试模式：只处理前2个问题'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别'
    )
    
    parser.add_argument(
        '--run-id',
        type=str,
        default=None,
        help='运行ID（用于标识本次运行）'
    )
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    if not args.generate:
        parser.print_help()
        return
    
    try:
        # 初始化生成器
        logger.info("初始化Counterfactual Generator...")
        generator = CounterfactualGenerator(config_path=args.config)
        
        # 加载问题
        logger.info("加载问题数据...")
        questions = generator.load_questions(args.questions)
        
        if args.test:
            logger.info("测试模式：只处理前2个问题")
            questions = questions[:2]
        
        if not questions:
            logger.error("没有找到问题数据")
            return
        
        logger.info(f"准备处理 {len(questions)} 个问题")
        
        # 生成响应
        logger.info("开始生成响应...")
        output_file = generator.generate_responses(
            questions=questions,
            run_id=args.run_id
        )
        
        # 生成汇总报告
        logger.info("生成汇总报告...")
        summary_file = generator.generate_summary_report(output_file)
        
        logger.info("=" * 60)
        logger.info("🎉 生成完成！")
        logger.info(f"📄 响应数据: {output_file}")
        if summary_file:
            logger.info(f"📊 汇总报告: {summary_file}")
        logger.info("=" * 60)
        
    except KeyboardInterrupt:
        logger.info("用户中断了程序")
    except Exception as e:
        logger.error(f"程序执行出错: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    main()
