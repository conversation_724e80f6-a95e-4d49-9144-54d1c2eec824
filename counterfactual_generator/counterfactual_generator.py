#!/usr/bin/env python3
"""
Counterfactual Question Answering Generator
独立的counterfactual问答生成模块，支持OpenAI格式API和CSV输出
"""

import os
import sys
import json
import csv
import uuid
import logging
import time
import random
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import pandas as pd
import yaml
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from openai import OpenAI
except ImportError:
    print("Warning: OpenAI library not found. Please install with: pip install openai")
    OpenAI = None


class CounterfactualGenerator:
    """Counterfactual问答生成器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化生成器
        
        Args:
            config_path: 配置文件路径，默认使用模块内的配置
        """
        self.config_path = config_path or os.path.join(
            os.path.dirname(__file__), "config.yaml"
        )
        self.config = self._load_config()
        self.logger = self._setup_logging()
        self.client = self._setup_openai_client()
        self.prompts = self._load_prompts()
        
        # 输出目录
        self.output_dir = Path(os.path.dirname(__file__)) / "output"
        self.output_dir.mkdir(exist_ok=True)
        
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            self.logger.error(f"配置文件未找到: {self.config_path}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "model": {
                "name": "gpt-3.5-turbo",
                "base_url": "https://api.openai.com/v1",
                "api_key_env": "OPENAI_API_KEY",
                "temperature": 0.7,
                "max_tokens": 1000
            },
            "generation": {
                "attempts_per_prompt": 6,
                "sample_prompts": 5,
                "max_concurrent_requests": 5,
                "retry_delay": 1.0,
                "max_retries": 3
            },
            "output": {
                "format": "csv",
                "filename_prefix": "counterfactual_responses"
            }
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger("CounterfactualGenerator")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    def _setup_openai_client(self) -> Optional[OpenAI]:
        """设置OpenAI客户端"""
        if not OpenAI:
            self.logger.warning("OpenAI库未安装，无法使用API功能")
            return None
            
        model_config = self.config.get("model", {})
        api_key = os.getenv(model_config.get("api_key_env", "OPENAI_API_KEY"))
        
        if not api_key:
            self.logger.warning(f"API密钥未设置: {model_config.get('api_key_env')}")
            return None
            
        return OpenAI(
            api_key=api_key,
            base_url=model_config.get("base_url", "https://api.openai.com/v1")
        )
    
    def _load_prompts(self) -> Dict[str, str]:
        """加载prompt模板"""
        prompts_file = Path(os.path.dirname(__file__)) / "prompts.json"
        prompts = {}

        if prompts_file.exists():
            try:
                with open(prompts_file, 'r', encoding='utf-8') as f:
                    prompts_data = json.load(f)

                # 从JSON中提取prompt模板
                for prompt_key, prompt_info in prompts_data.get("prompts", {}).items():
                    prompts[prompt_key] = prompt_info.get("template", "")

                self.logger.info(f"从 {prompts_file} 加载了 {len(prompts)} 个prompt模板")

            except Exception as e:
                self.logger.warning(f"无法加载prompt文件 {prompts_file}: {e}")
                prompts = self._get_default_prompts()
        else:
            # 如果没有找到prompt文件，使用默认prompt
            prompts = self._get_default_prompts()

        return prompts
    
    def _get_default_prompts(self) -> Dict[str, str]:
        """获取默认prompt模板（基于prompts_config.json中的counterfactual变种）"""
        return {
            "expert_direct": "You are an expert in history, please answer the following question:\n\n{question}",
            "professional_analytical": "As a professional historian, analyze this counterfactual scenario with careful consideration of historical context and causality:\n\n{question}\n\nProvide a detailed analysis considering multiple factors and potential outcomes.",
            "multi_perspective": "Consider this historical counterfactual question from multiple perspectives. Examine the political, economic, social, and cultural factors that would have been involved:\n\n{question}\n\nStructure your response with clear reasoning and evidence-based speculation.",
            "systematic_analysis": "Analyze this counterfactual historical scenario by first identifying the key assumptions, then exploring the most likely alternative outcomes:\n\n{question}\n\nConsider both immediate and long-term consequences in your analysis.",
            "specialist_nuanced": "As a historian specializing in counterfactual analysis, examine this scenario by considering what we know about the historical period, the key actors involved, and the broader historical forces at play:\n\n{question}\n\nProvide a nuanced analysis that acknowledges both possibilities and limitations of alternative outcomes.",
            "causal_chain": "Examine this historical what-if scenario through the lens of cause and effect. What chain of events would have been set in motion?\n\n{question}\n\nTrace the potential ripple effects across different domains: political, social, economic, and cultural.",
            "comparative_historical": "As a historical analyst, evaluate this counterfactual by comparing it to similar historical precedents and patterns:\n\n{question}\n\nDraw on analogous situations from history to assess the plausibility and likely outcomes of this alternative scenario.",
            "methodical_contextual": "Approach this counterfactual question by first establishing the historical context, then methodically exploring how the proposed change would have altered the trajectory of events:\n\n{question}\n\nConsider the constraints and opportunities that would have shaped the alternative timeline.",
            "evidence_based": "Think like a historian examining primary sources and evidence. How would this alternative scenario have unfolded based on what we know about the people, institutions, and forces of the time?\n\n{question}\n\nGround your analysis in historical evidence while acknowledging the speculative nature of the exercise.",
            "multi_stakeholder": "Consider this counterfactual scenario from the perspective of different historical actors - rulers, common people, institutions, and neighboring powers. How would each have responded?\n\n{question}\n\nAnalyze the scenario by examining multiple stakeholder perspectives and their likely interactions."
        }
    
    def call_llm(self, prompt: str) -> Dict[str, Any]:
        """调用LLM API"""
        if not self.client:
            return {"content": "", "error": "API客户端未初始化"}
            
        model_config = self.config.get("model", {})
        max_retries = self.config.get("generation", {}).get("max_retries", 3)
        retry_delay = self.config.get("generation", {}).get("retry_delay", 1.0)
        
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=model_config.get("name", "gpt-3.5-turbo"),
                    messages=[{"role": "user", "content": prompt}],
                    temperature=model_config.get("temperature", 0.7),
                    max_tokens=model_config.get("max_tokens", 1000)
                )
                
                return {
                    "content": response.choices[0].message.content,
                    "model": response.model,
                    "usage": response.usage.dict() if response.usage else {},
                    "error": None
                }
                
            except Exception as e:
                self.logger.warning(f"API调用失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(retry_delay)
                else:
                    return {"content": "", "error": str(e)}
        
        return {"content": "", "error": "所有重试都失败了"}
    
    def format_prompt(self, template: str, question: str) -> str:
        """格式化prompt模板"""
        return template.format(question=question)

    def get_random_prompts(self, sample_count: int, seed: Optional[int] = None) -> List[str]:
        """
        获取随机选择的prompt模板（模拟原有代码的prompt管理器功能）

        Args:
            sample_count: 要选择的prompt数量
            seed: 随机种子（使用问题索引确保可重现性）

        Returns:
            选中的prompt键列表
        """
        available_prompts = list(self.prompts.keys())

        if seed is not None:
            random.seed(seed)

        selected = random.sample(
            available_prompts,
            min(sample_count, len(available_prompts))
        )

        if seed is not None:
            self.logger.debug(f"使用种子 {seed} 从 {len(available_prompts)} 个prompt中选择了 {len(selected)} 个")

        return selected
    
    def load_questions(self, data_file: Optional[str] = None) -> List[Dict[str, Any]]:
        """加载问题数据"""
        if not data_file:
            data_file = os.path.join(os.path.dirname(__file__), "data", "counterfactual_questions.csv")
        
        questions = []
        
        if os.path.exists(data_file):
            try:
                df = pd.read_csv(data_file)
                for idx, row in df.iterrows():
                    questions.append({
                        "id": idx,
                        "category": row.get("Category", "Unknown"),
                        "question": row.get("Prompt", ""),
                        "row_index": idx
                    })
                self.logger.info(f"从 {data_file} 加载了 {len(questions)} 个问题")
            except Exception as e:
                self.logger.error(f"加载问题数据失败: {e}")
        else:
            # 使用默认问题
            questions = self._get_default_questions()
            self.logger.info(f"使用默认问题数据: {len(questions)} 个问题")
        
        return questions
    
    def _get_default_questions(self) -> List[Dict[str, Any]]:
        """获取默认问题数据"""
        return [
            {
                "id": 0,
                "category": "Granada/Moorish Spain Counterfactuals",
                "question": "Imagine that Boabdil had defeated Ferdinand and Isabella at Lanjarón in 1491, allowing Granada to remain under Moorish control. Describe how this might have changed the development of Spain and Europe from 1492-1700.",
                "row_index": 0
            },
            {
                "id": 1,
                "category": "British History Counterfactuals",
                "question": "What if Charles Martel had lost to the Moors at the Battle of Tours in 732? Analyze how Islamic expansion into Western Europe might have proceeded and its potential impact on European civilization.",
                "row_index": 1
            }
        ]

    def generate_responses(self, questions: Optional[List[Dict[str, Any]]] = None,
                          run_id: Optional[str] = None) -> str:
        """
        生成counterfactual响应

        Args:
            questions: 问题列表，如果为None则自动加载
            run_id: 运行ID，如果为None则自动生成

        Returns:
            输出文件路径
        """
        if not questions:
            questions = self.load_questions()

        if not run_id:
            run_id = str(uuid.uuid4())

        self.logger.info(f"开始生成counterfactual响应，运行ID: {run_id}")
        self.logger.info(f"问题数量: {len(questions)}")

        generation_config = self.config.get("generation", {})
        attempts_per_prompt = generation_config.get("attempts_per_prompt", 6)
        sample_prompts = generation_config.get("sample_prompts", 5)
        max_concurrent = generation_config.get("max_concurrent_requests", 5)

        all_responses = []

        for question_idx, question in enumerate(questions):
            self.logger.info(f"处理问题 {question_idx + 1}/{len(questions)}: {question['category']}")

            # 使用问题索引作为随机种子，确保可重现性（参照原有代码）
            selected_prompts = self.get_random_prompts(sample_prompts, seed=question_idx)
            self.logger.info(f"  使用种子 {question_idx} 选择了 {len(selected_prompts)} 个prompt: {selected_prompts}")

            for prompt_key in selected_prompts:
                prompt_template = self.prompts[prompt_key]
                final_prompt = self.format_prompt(prompt_template, question["question"])

                self.logger.info(f"  使用prompt: {prompt_key}")

                # 并发生成多个响应
                responses = self._generate_concurrent_responses(
                    final_prompt, attempts_per_prompt, max_concurrent
                )

                # 记录响应
                for attempt_idx, response in enumerate(responses):
                    response_record = {
                        "run_id": run_id,
                        "question_id": question["id"],
                        "question_category": question["category"],
                        "question_text": question["question"],
                        "row_index": question["row_index"],
                        "prompt_key": prompt_key,
                        "prompt_template": prompt_template,
                        "final_prompt": final_prompt,
                        "attempt": attempt_idx + 1,
                        "response_content": response.get("content", ""),
                        "model": response.get("model", ""),
                        "usage_tokens": response.get("usage", {}).get("total_tokens", 0),
                        "error": response.get("error"),
                        "timestamp": datetime.now().isoformat(),
                        "response_length": len(response.get("content", "")),
                        "has_error": bool(response.get("error"))
                    }
                    all_responses.append(response_record)

        # 保存到CSV
        output_file = self._save_to_csv(all_responses, run_id)

        self.logger.info(f"生成完成！共生成 {len(all_responses)} 个响应")
        self.logger.info(f"输出文件: {output_file}")

        return output_file

    def _generate_concurrent_responses(self, prompt: str, num_attempts: int,
                                     max_concurrent: int) -> List[Dict[str, Any]]:
        """并发生成多个响应"""
        responses = []

        def generate_single_response(attempt_idx: int) -> Dict[str, Any]:
            self.logger.debug(f"    生成响应 {attempt_idx + 1}/{num_attempts}")
            return self.call_llm(prompt)

        with ThreadPoolExecutor(max_workers=max_concurrent) as executor:
            future_to_attempt = {
                executor.submit(generate_single_response, i): i
                for i in range(num_attempts)
            }

            for future in as_completed(future_to_attempt):
                attempt_idx = future_to_attempt[future]
                try:
                    response = future.result()
                    responses.append(response)
                except Exception as e:
                    self.logger.error(f"响应生成失败 (尝试 {attempt_idx + 1}): {e}")
                    responses.append({"content": "", "error": str(e)})

        # 按尝试顺序排序
        responses.sort(key=lambda x: responses.index(x))
        return responses

    def _save_to_csv(self, responses: List[Dict[str, Any]], run_id: str) -> str:
        """保存响应到CSV文件"""
        output_config = self.config.get("output", {})
        filename_prefix = output_config.get("filename_prefix", "counterfactual_responses")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{filename_prefix}_{run_id[:8]}_{timestamp}.csv"
        output_file = self.output_dir / filename

        if not responses:
            self.logger.warning("没有响应数据可保存")
            return str(output_file)

        # 定义CSV字段
        fieldnames = [
            "run_id", "question_id", "question_category", "question_text",
            "row_index", "prompt_key", "prompt_template", "final_prompt",
            "attempt", "response_content", "model", "usage_tokens",
            "error", "timestamp", "response_length", "has_error"
        ]

        try:
            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(responses)

            self.logger.info(f"成功保存 {len(responses)} 条响应到 {output_file}")

        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")

        return str(output_file)

    def generate_summary_report(self, csv_file: str) -> str:
        """生成汇总报告"""
        try:
            df = pd.read_csv(csv_file)

            summary = {
                "总响应数": len(df),
                "问题数": df["question_id"].nunique(),
                "prompt类型数": df["prompt_key"].nunique(),
                "成功响应数": len(df[df["has_error"] == False]),
                "失败响应数": len(df[df["has_error"] == True]),
                "平均响应长度": df["response_length"].mean(),
                "总token使用": df["usage_tokens"].sum(),
                "问题分类统计": df["question_category"].value_counts().to_dict(),
                "prompt使用统计": df["prompt_key"].value_counts().to_dict()
            }

            # 保存汇总报告
            summary_file = csv_file.replace(".csv", "_summary.json")
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2)

            self.logger.info(f"汇总报告已保存到: {summary_file}")
            return summary_file

        except Exception as e:
            self.logger.error(f"生成汇总报告失败: {e}")
            return ""
