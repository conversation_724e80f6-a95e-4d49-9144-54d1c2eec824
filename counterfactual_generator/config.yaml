# Counterfactual Generator Configuration
# 独立的counterfactual问答生成器配置文件

# 模型配置
model:
  # 模型名称 - 支持OpenAI格式的API
  name: "gpt-3.5-turbo"  # 可改为 "gpt-4", "gpt-4-turbo" 等
  
  # API配置
  base_url: "https://api.openai.com/v1"  # OpenAI API地址
  api_key_env: "OPENAI_API_KEY"  # 环境变量名
  
  # 生成参数
  temperature: 0.7  # 控制随机性，0.0-2.0
  max_tokens: 1000  # 最大生成token数
  top_p: 0.95      # 核采样参数
  
  # 其他可选参数
  frequency_penalty: 0.0
  presence_penalty: 0.0

# 生成配置
generation:
  # 每个prompt的尝试次数
  attempts_per_prompt: 6
  
  # 从所有prompt中随机选择的数量
  sample_prompts: 5
  
  # 并发请求数量
  max_concurrent_requests: 5
  
  # 重试配置
  max_retries: 3
  retry_delay: 1.0  # 重试间隔（秒）
  
  # 请求间隔（避免API限制）
  request_delay: 0.1

# 输出配置
output:
  # 输出格式
  format: "csv"  # 目前只支持csv
  
  # 文件名前缀
  filename_prefix: "counterfactual_responses"
  
  # 是否生成汇总报告
  generate_summary: true
  
  # 输出目录（相对于模块目录）
  output_dir: "output"

# 数据配置
data:
  # 问题数据文件（相对于模块目录）
  questions_file: "data/counterfactual_questions.csv"
  
  # 问题字段映射
  field_mapping:
    category_field: "Category"
    question_field: "Prompt"
    id_field: "row_index"

# Prompt配置
prompts:
  # Prompt模板文件（JSON格式，相对于模块目录）
  templates_file: "prompts.json"

  # 默认使用的prompt类型（基于prompts_config.json中的counterfactual变种）
  default_prompts:
    - "expert_direct"
    - "professional_analytical"
    - "multi_perspective"
    - "systematic_analysis"
    - "specialist_nuanced"
    - "causal_chain"
    - "comparative_historical"
    - "methodical_contextual"
    - "evidence_based"
    - "multi_stakeholder"

  # 是否随机选择prompt
  random_selection: true

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件（可选）
  file: null  # 如果设置，会同时输出到文件
  
  # 控制台输出
  console: true

# 系统配置
system:
  # 随机种子（用于可重现的结果）
  random_seed: null  # 如果设置，会固定随机选择
  
  # 进度报告间隔
  progress_report_interval: 10
  
  # 是否在出错时继续
  continue_on_error: true

# 实验配置（可选）
experiment:
  # 实验名称
  name: "counterfactual_generation"
  
  # 实验描述
  description: "Historical counterfactual question answering generation"
  
  # 实验标签
  tags:
    - "counterfactual"
    - "historical"
    - "qa"
  
  # 实验元数据
  metadata:
    version: "1.0"
    author: "Counterfactual Generator"
    created_date: "2025-08-25"
