# Counterfactual Generator

独立的历史反事实问答生成模块，支持OpenAI格式API和CSV输出。

## 功能特点

- 🤖 支持OpenAI格式API（可轻松切换到GPT模型）
- 📊 CSV格式输出（替代MongoDB）
- 🎯 10种不同的prompt变种
- ⚡ 并发生成支持
- 📈 自动生成汇总报告
- 🔧 灵活的配置系统

## 目录结构

```
counterfactual_generator/
├── counterfactual_generator.py  # 主生成器类
├── run_generator.py            # 运行脚本
├── config.yaml                 # 配置文件
├── prompts.json                # Prompt模板文件（JSON格式）
├── README.md                   # 说明文档
├── requirements.txt            # 依赖文件
├── data/
│   └── counterfactual_questions.csv  # 问题数据
└── output/                     # 输出目录
    ├── counterfactual_responses_*.csv
    └── counterfactual_responses_*_summary.json
```

## 安装依赖

```bash
pip install openai pandas pyyaml
```

## 配置

### 1. 设置API密钥

```bash
export OPENAI_API_KEY="your-api-key-here"
```

### 2. 修改配置文件

编辑 `config.yaml` 文件：

```yaml
model:
  name: "gpt-3.5-turbo"  # 或 "gpt-4", "gpt-4-turbo"
  base_url: "https://api.openai.com/v1"
  api_key_env: "OPENAI_API_KEY"
  temperature: 0.7
  max_tokens: 1000

generation:
  attempts_per_prompt: 6      # 每个prompt生成6次
  sample_prompts: 5           # 从10个prompt中随机选5个
  max_concurrent_requests: 5  # 并发请求数
```

## 使用方法

### 基本使用

```bash
# 使用默认配置生成
python run_generator.py --generate

# 测试模式（只处理前2个问题）
python run_generator.py --generate --test

# 使用自定义配置
python run_generator.py --generate --config custom_config.yaml

# 使用自定义问题文件
python run_generator.py --generate --questions my_questions.csv
```

### 高级选项

```bash
# 设置日志级别
python run_generator.py --generate --log-level DEBUG

# 指定运行ID
python run_generator.py --generate --run-id my_experiment_001
```

### 编程接口

```python
from counterfactual_generator import CounterfactualGenerator

# 初始化生成器
generator = CounterfactualGenerator("config.yaml")

# 加载问题
questions = generator.load_questions("data/counterfactual_questions.csv")

# 生成响应
output_file = generator.generate_responses(questions)

# 生成汇总报告
summary_file = generator.generate_summary_report(output_file)
```

## Prompt变种

模块包含10种不同的prompt变种，基于原始的prompts_config.json：

1. **expert_direct** - 专家直接回答
2. **professional_analytical** - 专业分析师风格
3. **multi_perspective** - 多角度分析
4. **systematic_analysis** - 系统性分析
5. **specialist_nuanced** - 专业细致分析
6. **causal_chain** - 因果链分析
7. **comparative_historical** - 历史比较分析
8. **methodical_contextual** - 方法论上下文分析
9. **evidence_based** - 基于证据的分析
10. **multi_stakeholder** - 多利益相关者分析

## 输出格式

### CSV文件字段

- `run_id` - 运行ID
- `question_id` - 问题ID
- `question_category` - 问题分类
- `question_text` - 问题文本
- `prompt_key` - 使用的prompt类型
- `attempt` - 尝试次数
- `response_content` - 生成的响应内容
- `model` - 使用的模型
- `usage_tokens` - 使用的token数
- `timestamp` - 生成时间
- `response_length` - 响应长度
- `has_error` - 是否有错误

### 汇总报告

JSON格式的汇总报告包含：
- 总响应数统计
- 成功/失败率
- 平均响应长度
- Token使用统计
- 问题分类统计
- Prompt使用统计

## 自定义问题数据

问题CSV文件格式：

```csv
Category,Prompt
"Granada/Moorish Spain Counterfactuals","Imagine that Boabdil had defeated Ferdinand..."
"British History Counterfactuals","What if Charles Martel had lost to the Moors..."
```

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   确保设置了正确的环境变量：export OPENAI_API_KEY="your-key"
   ```

2. **依赖缺失**
   ```bash
   pip install openai pandas pyyaml
   ```

3. **配置文件错误**
   ```
   检查config.yaml语法是否正确
   ```

### 日志文件

程序会生成 `counterfactual_generator.log` 日志文件，包含详细的执行信息。

## 扩展功能

### 添加新的Prompt

1. 在 `prompts.json` 文件中添加新的prompt条目
2. 在 `config.yaml` 的 `default_prompts` 中添加新的prompt键名
3. 重新运行程序

示例：在 `prompts.json` 中添加新prompt：
```json
{
  "prompts": {
    "new_prompt_style": {
      "id": "counterfactual_11",
      "template": "Your new prompt template here:\n\n{question}",
      "style": "new_style",
      "reasoning": true
    }
  }
}
```

### 支持其他API

修改 `_setup_openai_client()` 方法以支持其他兼容OpenAI格式的API。

## 许可证

本模块基于原始LLM不确定性分析系统开发，保持相同的许可证。
