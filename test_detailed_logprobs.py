#!/usr/bin/env python3
"""
测试详细的logprobs保存功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vllm_sentment_generator import VLLMSentimentGenerator
import logging
import json

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_detailed_logprobs():
    """测试详细logprobs保存功能"""
    
    # 初始化生成器
    try:
        generator = VLLMSentimentGenerator(
            vllm_host="http://localhost:8000",
            api_key="token-abc123",
            model_name="Qwen/Qwen3-14B-FP8"
        )
        logger.info("VLLM Sentiment Generator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize generator: {e}")
        return
    
    # 测试一个简单的文本
    test_text = "I love this beautiful sunny day! 😊"
    
    try:
        # 分析情感
        result = generator.analyze_sentiment_with_logprobs(test_text)
        
        print("\n=== Basic Analysis Result ===")
        print(f"Text: {test_text}")
        print(f"Raw Response: {result['raw_response']}")
        print(f"Predicted Sentiment: {result['predicted_sentiment']}")
        print(f"Confidence: {result['confidence']:.4f}")
        print(f"Total Logprob: {result['total_logprob']:.4f}")
        
        # 测试详细logprobs提取
        if result.get('logprobs'):
            detailed_logprobs = generator._extract_detailed_logprobs(result['logprobs'])
            
            print(f"\n=== Detailed Logprobs ===")
            print(f"Number of tokens: {len(detailed_logprobs)}")
            
            for token_info in detailed_logprobs:
                print(f"Position {token_info['position']}: '{token_info['token']}' "
                      f"(logprob: {token_info['logprob']:.4f}, prob: {token_info['prob']:.4f})")
            
            # 保存到JSON文件查看完整结构
            output_data = {
                "text": test_text,
                "result": result,
                "detailed_logprobs": detailed_logprobs
            }
            
            with open("test_logprobs_output.json", "w", encoding="utf-8") as f:
                json.dump(output_data, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"\n=== Saved detailed results to test_logprobs_output.json ===")
            
        else:
            print("No logprobs data available")
            
    except Exception as e:
        logger.error(f"Error during analysis: {e}")
        import traceback
        traceback.print_exc()

def test_dataset_processing():
    """测试数据集处理功能"""
    
    # 初始化生成器
    try:
        generator = VLLMSentimentGenerator(
            vllm_host="http://localhost:8000",
            api_key="token-abc123",
            model_name="Qwen/Qwen3-14B-FP8"
        )
        logger.info("VLLM Sentiment Generator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize generator: {e}")
        return
    
    # 加载数据集
    dataset = generator.load_dataset_from_config()
    print(f"\n=== Dataset Info ===")
    print(f"Total records: {len(dataset)}")
    
    if dataset:
        print(f"First record: {dataset[0]}")
        print(f"Sample text: {dataset[0]['text'][:100]}...")
        print(f"Sample label: {dataset[0]['label']}")

if __name__ == "__main__":
    print("Testing detailed logprobs functionality...")
    test_detailed_logprobs()
    
    print("\n" + "="*50)
    print("Testing dataset loading...")
    test_dataset_processing()
