#!/usr/bin/env python3
"""
Sentiment Analysis Token Entropy Calculator
专门计算情感分析结果的token级别熵
"""

import os
import sys
import argparse
import logging
import numpy as np
from typing import List, Dict, Any, Optional
from pymongo import MongoClient
from tqdm import tqdm
from datetime import datetime, timezone

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from uq_methods.implementations.token_entropy import TokenEntropyUQ, MeanTokenEntropyUQ

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SentimentTokenEntropyCalculator:
    """专门用于计算情感分析的token entropy"""
    
    def __init__(self, mongo_host: str = 'localhost', mongo_port: int = 27017, 
                 mongo_db: str = 'LLM-UQ', verbose: bool = False):
        """
        初始化计算器
        
        Args:
            mongo_host: MongoDB主机
            mongo_port: MongoDB端口
            mongo_db: 数据库名称
            verbose: 是否显示详细信息
        """
        self.mongo_host = mongo_host
        self.mongo_port = mongo_port
        self.mongo_db = mongo_db
        self.verbose = verbose
        
        # 初始化UQ方法
        self.token_entropy_uq = TokenEntropyUQ(verbose=verbose)
        self.mean_token_entropy_uq = MeanTokenEntropyUQ(verbose=verbose)
        
        # 连接MongoDB
        self.client = MongoClient(host=mongo_host, port=mongo_port)
        self.db = self.client[mongo_db]
        
    def get_sentiment_response_groups(self, collection_name: str = 'response_collection_no_thinking') -> List[Dict[str, Any]]:
        """
        获取情感分析的响应分组
        
        Args:
            collection_name: 响应集合名称
            
        Returns:
            分组后的响应列表
        """
        collection = self.db[collection_name]
        
        # 按照输入文本分组，获取每组的所有响应
        pipeline = [
            {
                "$match": {
                    "task_category": "sentiment_analysis",
                    "parsed_answer": {"$ne": None},  # 确保有解析结果
                    "response_logprobs": {"$ne": None}  # 确保有logprobs数据
                }
            },
            {
                "$group": {
                    "_id": {
                        "input_text": "$input_text",
                        "prompt_seed": "$prompt_seed"
                    },
                    "responses": {
                        "$push": {
                            "parsed_answer": "$parsed_answer",
                            "raw_response": "$raw_response",
                            "response_logprobs": "$response_logprobs",
                            "detailed_logprobs": "$detailed_logprobs",
                            "total_logprob": "$total_logprob",
                            "confidence": "$confidence",
                            "sentiment_probabilities": "$sentiment_probabilities"
                        }
                    },
                    "meta": {
                        "$first": {
                            "input_text": "$input_text",
                            "prompt_seed": "$prompt_seed",
                            "task_name": "$task_name",
                            "dataset_source": "$dataset_source",
                            "model_identifier": "$model_identifier",
                            "reference_answer": "$reference_answer"
                        }
                    }
                }
            }
        ]
        
        groups = list(collection.aggregate(pipeline))
        logger.info(f"Found {len(groups)} sentiment analysis response groups")
        
        return groups
    
    def compute_token_entropy_for_group(self, group: Dict[str, Any]) -> Dict[str, Any]:
        """
        为一个响应组计算token entropy
        
        Args:
            group: 响应组数据
            
        Returns:
            包含token entropy结果的字典
        """
        responses = group['responses']
        meta = group['meta']
        
        if len(responses) < 2:
            logger.warning(f"Group has only {len(responses)} responses, skipping")
            return None
        
        # 提取响应文本和logprobs数据
        response_texts = []
        responses_with_probs = []
        
        for resp in responses:
            if resp.get('parsed_answer') and resp.get('response_logprobs'):
                response_texts.append(str(resp['parsed_answer']))
                responses_with_probs.append({
                    'logprobs': resp['response_logprobs'],
                    'raw_response': resp.get('raw_response', ''),
                    'detailed_logprobs': resp.get('detailed_logprobs', [])
                })
        
        if len(response_texts) < 2:
            logger.warning(f"Group has insufficient valid responses with logprobs: {len(response_texts)}")
            return None
        
        # 计算Token Entropy
        token_entropy_result = self.token_entropy_uq.compute_uncertainty(
            response_texts, responses_with_probs
        )
        
        # 计算Mean Token Entropy
        mean_token_entropy_result = self.mean_token_entropy_uq.compute_uncertainty(
            response_texts, responses_with_probs
        )
        
        # 构建结果
        result = {
            "group_key": {
                "input_text": meta['input_text'],
                "prompt_seed": meta['prompt_seed'],
                "task_name": meta.get('task_name', 'sentiment_analysis'),
                "dataset_source": meta.get('dataset_source', 'unknown')
            },
            "meta_info": {
                "model_identifier": meta.get('model_identifier', 'unknown'),
                "reference_answer": meta.get('reference_answer', ''),
                "num_responses": len(response_texts),
                "input_text": meta['input_text']
            },
            "token_entropy": token_entropy_result,
            "mean_token_entropy": mean_token_entropy_result,
            "responses_summary": {
                "response_texts": response_texts,
                "unique_responses": list(set(response_texts)),
                "response_distribution": self._get_response_distribution(response_texts)
            },
            "computation_timestamp": datetime.now(timezone.utc)
        }
        
        if self.verbose:
            logger.info(f"Computed token entropy for input: '{meta['input_text'][:50]}...'")
            logger.info(f"  Token Entropy Score: {token_entropy_result.get('uncertainty_score', 'N/A')}")
            logger.info(f"  Mean Token Entropy Score: {mean_token_entropy_result.get('uncertainty_score', 'N/A')}")
            logger.info(f"  Responses: {response_texts}")
        
        return result
    
    def _get_response_distribution(self, responses: List[str]) -> Dict[str, int]:
        """获取响应分布统计"""
        distribution = {}
        for resp in responses:
            distribution[resp] = distribution.get(resp, 0) + 1
        return distribution
    
    def save_results(self, results: List[Dict[str, Any]], 
                    output_collection: str = 'sentiment_token_entropy_results'):
        """
        保存结果到MongoDB
        
        Args:
            results: 计算结果列表
            output_collection: 输出集合名称
        """
        if not results:
            logger.warning("No results to save")
            return
        
        collection = self.db[output_collection]
        
        # 创建索引以提高查询性能
        collection.create_index([
            ("group_key.input_text", 1),
            ("group_key.prompt_seed", 1)
        ])
        
        # 批量插入结果
        try:
            result = collection.insert_many(results)
            logger.info(f"Successfully saved {len(result.inserted_ids)} token entropy results to {output_collection}")
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def run_analysis(self, input_collection: str = 'response_collection_no_thinking',
                    output_collection: str = 'sentiment_token_entropy_results',
                    max_groups: Optional[int] = None):
        """
        运行完整的token entropy分析
        
        Args:
            input_collection: 输入集合名称
            output_collection: 输出集合名称
            max_groups: 最大处理组数（用于测试）
        """
        logger.info("Starting sentiment token entropy analysis...")
        
        # 获取响应分组
        groups = self.get_sentiment_response_groups(input_collection)
        
        if max_groups:
            groups = groups[:max_groups]
            logger.info(f"Limited to {max_groups} groups for testing")
        
        # 计算token entropy
        results = []
        for group in tqdm(groups, desc="Computing token entropy"):
            result = self.compute_token_entropy_for_group(group)
            if result:
                results.append(result)
        
        logger.info(f"Successfully computed token entropy for {len(results)} groups")
        
        # 保存结果
        if results:
            self.save_results(results, output_collection)
        
        return results
    
    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()


def main():
    parser = argparse.ArgumentParser(description="Compute token entropy for sentiment analysis results")
    parser.add_argument('--mongo_host', type=str, default='localhost', help='MongoDB host')
    parser.add_argument('--mongo_port', type=int, default=27017, help='MongoDB port')
    parser.add_argument('--mongo_db', type=str, default='LLM-UQ', help='MongoDB database name')
    parser.add_argument('--input_collection', type=str, default='response_collection_no_thinking',
                       help='Input collection name')
    parser.add_argument('--output_collection', type=str, default='sentiment_token_entropy_results',
                       help='Output collection name')
    parser.add_argument('--max_groups', type=int, default=None,
                       help='Maximum number of groups to process (for testing)')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # 创建计算器
    calculator = SentimentTokenEntropyCalculator(
        mongo_host=args.mongo_host,
        mongo_port=args.mongo_port,
        mongo_db=args.mongo_db,
        verbose=args.verbose
    )
    
    try:
        # 运行分析
        results = calculator.run_analysis(
            input_collection=args.input_collection,
            output_collection=args.output_collection,
            max_groups=args.max_groups
        )
        
        logger.info(f"Analysis completed successfully. Processed {len(results)} groups.")
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        raise
    finally:
        calculator.close()


if __name__ == "__main__":
    main()
