#!/usr/bin/env python3
"""
恢复采样数据脚本
基于原有的data_sampler.py逻辑重新生成采样数据
"""

import os
import csv
import pandas as pd
import random
from pymongo import MongoClient
from typing import List, Dict, Any


def restore_semeval_sampling(sample_size: int = 500) -> bool:
    """恢复SemEval采样数据"""
    print(f"🔄 恢复SemEval采样数据 (样本数: {sample_size})")
    
    # 检查原始数据文件
    original_file = 'data/SemEval2017-task4-test.subtask-A.english.txt'
    csv_file = 'data/SemEval2017-task4-test.subtask-A.english.csv'
    
    if not os.path.exists(original_file) and not os.path.exists(csv_file):
        print(f"❌ 原始SemEval数据文件不存在: {original_file} 或 {csv_file}")
        return False
    
    try:
        # 如果是txt文件，先转换为CSV格式
        if os.path.exists(original_file) and not os.path.exists(csv_file):
            print("📝 转换TXT文件为CSV格式...")
            convert_txt_to_csv(original_file, csv_file)
        
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        print(f"📊 原始数据: {len(df)} 条记录")
        
        # 使用相同的随机种子进行采样
        sampled_df = df.sample(n=min(sample_size, len(df)), random_state=42)
        
        # 保存采样数据
        output_file = 'sampled_semeval.csv'
        sampled_df.to_csv(output_file, index=False)
        
        print(f"✅ 成功生成 {len(sampled_df)} 条SemEval采样数据到 {output_file}")
        print(f"   标签分布: {sampled_df['label'].value_counts().to_dict()}")
        
        return True
        
    except Exception as e:
        print(f"❌ SemEval采样失败: {e}")
        return False


def convert_txt_to_csv(txt_file: str, csv_file: str):
    """将TXT格式的SemEval数据转换为CSV格式"""
    data = []
    
    with open(txt_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line:
                continue
                
            # 假设格式为: id\ttext\tlabel 或类似格式
            parts = line.split('\t')
            if len(parts) >= 3:
                data.append({
                    'id': parts[0],
                    'text': parts[1], 
                    'label': parts[2]
                })
            else:
                # 如果格式不同，尝试其他解析方式
                print(f"⚠️  第{line_num}行格式异常: {line[:50]}...")
    
    # 保存为CSV
    df = pd.DataFrame(data)
    df.to_csv(csv_file, index=False)
    print(f"📄 转换完成: {len(data)} 条记录保存到 {csv_file}")


def restore_commits_sampling_from_mongodb(sample_size: int = 500) -> bool:
    """从MongoDB恢复Commits采样数据"""
    print(f"🔄 从MongoDB恢复Commits采样数据 (样本数: {sample_size})")
    
    try:
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['pytorch-sha']
        collection = db['pytorch-commits']
        
        # 检查数据是否存在
        total_count = collection.count_documents({})
        if total_count == 0:
            print("❌ MongoDB中没有找到pytorch-commits数据")
            client.close()
            return False
            
        print(f"📊 MongoDB中共有 {total_count} 条提交记录")
        
        # 使用聚合管道随机采样
        pipeline = [{"$sample": {"size": sample_size}}]
        
        sampled_data = []
        for doc in collection.aggregate(pipeline):
            sampled_data.append({
                'sha': doc.get('sha', ''),
                'message': doc.get('commit', {}).get('message', ''),
                'date': doc.get('commit', {}).get('author', {}).get('date', ''),
                'author': doc.get('commit', {}).get('author', {}).get('name', '')
            })
        
        client.close()
        
        # 保存采样数据到CSV
        output_file = 'sampled_commits.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_data)
        
        print(f"✅ 成功生成 {len(sampled_data)} 条Commits采样数据到 {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ Commits采样失败: {e}")
        return False


def restore_commits_sampling_from_response_collections(sample_size: int = 500) -> bool:
    """从response_collections恢复Commits采样数据"""
    print(f"🔄 从response_collections恢复Commits采样数据")
    
    try:
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询explorative_coding任务的数据
        pipeline = [
            {"$match": {"task_name": "explorative_coding"}},
            {"$group": {
                "_id": "$commit_sha",
                "message": {"$first": "$commit_message"},
                "date": {"$first": "$commit_date"},
                "author": {"$first": "$commit_author"}
            }},
            {"$limit": sample_size}
        ]
        
        sampled_data = []
        for doc in collection.aggregate(pipeline):
            sampled_data.append({
                'sha': doc['_id'],
                'message': doc.get('message', ''),
                'date': doc.get('date', ''),
                'author': doc.get('author', '')
            })
        
        client.close()
        
        if not sampled_data:
            print("❌ 在response_collections中没有找到explorative_coding数据")
            return False
        
        # 保存采样数据到CSV
        output_file = 'sampled_commits.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_data)
        
        print(f"✅ 从response_collections恢复 {len(sampled_data)} 条Commits采样数据到 {output_file}")
        return True
        
    except Exception as e:
        print(f"❌ 从response_collections恢复Commits采样失败: {e}")
        return False


def check_mongodb_status():
    """检查MongoDB状态和数据"""
    print("🔍 检查MongoDB状态...")
    
    try:
        client = MongoClient('localhost', 27017)
        
        # 检查LLM-UQ数据库
        db_llm_uq = client['LLM-UQ']
        collections_llm_uq = db_llm_uq.list_collection_names()
        print(f"📊 LLM-UQ数据库集合: {collections_llm_uq}")
        
        if 'response_collections' in collections_llm_uq:
            count = db_llm_uq['response_collections'].count_documents({})
            print(f"   response_collections: {count} 条记录")
            
            # 检查任务类型
            tasks = db_llm_uq['response_collections'].distinct('task_name')
            print(f"   任务类型: {tasks}")
        
        # 检查pytorch-sha数据库
        db_pytorch = client['pytorch-sha']
        collections_pytorch = db_pytorch.list_collection_names()
        print(f"📊 pytorch-sha数据库集合: {collections_pytorch}")
        
        if 'pytorch-commits' in collections_pytorch:
            count = db_pytorch['pytorch-commits'].count_documents({})
            print(f"   pytorch-commits: {count} 条记录")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB连接失败: {e}")
        return False


def main():
    """主函数"""
    print("🔄 数据采样恢复脚本")
    print("=" * 50)
    
    # 检查MongoDB状态
    mongodb_ok = check_mongodb_status()
    
    print("\n" + "=" * 50)
    
    # 恢复SemEval采样数据
    semeval_ok = restore_semeval_sampling(500)
    
    print("\n" + "-" * 30)
    
    # 恢复Commits采样数据
    commits_ok = False
    if mongodb_ok:
        # 先尝试从原始pytorch-commits采样
        commits_ok = restore_commits_sampling_from_mongodb(500)
        
        # 如果失败，尝试从response_collections恢复
        if not commits_ok:
            commits_ok = restore_commits_sampling_from_response_collections(500)
    
    print("\n" + "=" * 50)
    print("📋 恢复结果总结:")
    print(f"   SemEval采样: {'✅ 成功' if semeval_ok else '❌ 失败'}")
    print(f"   Commits采样: {'✅ 成功' if commits_ok else '❌ 失败'}")
    print(f"   MongoDB状态: {'✅ 正常' if mongodb_ok else '❌ 异常'}")
    
    if semeval_ok and commits_ok:
        print("\n🎉 所有采样数据恢复成功！")
    else:
        print("\n⚠️  部分数据恢复失败，请检查原始数据源")


if __name__ == "__main__":
    main()
