#!/usr/bin/env python3
"""
测试 Phi-4 本地模型的功能
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from phi4_local_runner import Phi4LocalRunner
except ImportError as e:
    print(f"Error importing Phi4LocalRunner: {e}")
    print("Make sure phi4_local_runner.py is in the same directory")
    sys.exit(1)

def find_model_file():
    """查找模型文件"""
    possible_names = [
        "phi-4-Q4_K_M.gguf",
        "phi4-Q4_K_M.gguf",
        "phi-4.gguf",
        "phi4.gguf"
    ]
    
    possible_dirs = [
        Path("."),
        Path("models"),
        Path("./models"),
        Path.home() / "models",
        Path("/models"),
    ]
    
    for directory in possible_dirs:
        for name in possible_names:
            model_path = directory / name
            if model_path.exists():
                return str(model_path)
    
    return None

def test_model_loading():
    """测试模型加载"""
    print("=" * 50)
    print("Testing Phi-4 Model Loading")
    print("=" * 50)
    
    # 查找模型文件
    model_path = find_model_file()
    
    if not model_path:
        print("❌ Model file not found!")
        print("Please make sure phi-4-Q4_K_M.gguf is in one of these locations:")
        print("  - Current directory")
        print("  - ./models/")
        print("  - ~/models/")
        print("")
        model_path = input("Enter the full path to your model file (or press Enter to skip): ").strip()
        if not model_path:
            return None
    
    print(f"📁 Found model at: {model_path}")
    
    try:
        print("🔄 Loading model...")
        runner = Phi4LocalRunner(
            model_path=model_path,
            n_ctx=2048,  # 较小的上下文以节省内存
            verbose=False
        )
        print("✅ Model loaded successfully!")
        return runner
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None

def test_sentiment_analysis(runner):
    """测试情感分析功能"""
    print("\n" + "=" * 50)
    print("Testing Sentiment Analysis")
    print("=" * 50)
    
    test_texts = [
        "I love this beautiful sunny day! 😊",
        "This is the worst movie I've ever seen.",
        "The weather is okay today, nothing special.",
        "Amazing performance by the team!",
        "I hate waiting in long lines."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Test {i}: {text}")
        try:
            result = runner.sentiment_analysis(text)
            print(f"   Sentiment: {result['sentiment']}")
            print(f"   Confidence: {result['confidence']:.4f}")
            print(f"   Raw Response: {result['raw_response']}")
            print(f"   Time: {result['generation_time']:.2f}s")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def test_text_generation(runner):
    """测试文本生成功能"""
    print("\n" + "=" * 50)
    print("Testing Text Generation")
    print("=" * 50)
    
    test_prompts = [
        "The weather today is",
        "Artificial intelligence is",
        "My favorite food is"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 Test {i}: {prompt}")
        try:
            result = runner.generate_text(
                prompt=prompt,
                max_tokens=50,
                temperature=0.7
            )
            print(f"   Generated: {result['text']}")
            print(f"   Time: {result['generation_time']:.2f}s")
        except Exception as e:
            print(f"   ❌ Error: {e}")

def interactive_test(runner):
    """交互式测试"""
    print("\n" + "=" * 50)
    print("Interactive Testing Mode")
    print("=" * 50)
    print("Commands:")
    print("  s <text> - Sentiment analysis")
    print("  g <prompt> - Generate text")
    print("  q - Quit")
    print()
    
    while True:
        try:
            user_input = input("Test> ").strip()
            
            if user_input.lower() in ['q', 'quit', 'exit']:
                break
            elif user_input.startswith('s '):
                text = user_input[2:]
                result = runner.sentiment_analysis(text)
                print(f"Sentiment: {result['sentiment']} (confidence: {result['confidence']:.4f})")
            elif user_input.startswith('g '):
                prompt = user_input[2:]
                result = runner.generate_text(prompt, max_tokens=30)
                print(f"Generated: {result['text']}")
            else:
                print("Invalid command. Use 's <text>' for sentiment or 'g <prompt>' for generation")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")

def main():
    """主测试函数"""
    print("Phi-4 Local Model Test Suite")
    print("=" * 50)
    
    # 测试模型加载
    runner = test_model_loading()
    if not runner:
        print("Cannot proceed without a working model.")
        return 1
    
    # 测试情感分析
    test_sentiment_analysis(runner)
    
    # 测试文本生成
    test_text_generation(runner)
    
    # 询问是否进行交互式测试
    print("\n" + "=" * 50)
    response = input("Would you like to run interactive tests? (y/N): ").strip().lower()
    if response == 'y':
        interactive_test(runner)
    
    print("\n✅ All tests completed!")
    return 0

if __name__ == "__main__":
    exit(main())
