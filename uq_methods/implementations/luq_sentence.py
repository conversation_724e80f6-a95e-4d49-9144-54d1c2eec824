import numpy as np
import logging
from typing import List, Dict, Any, <PERSON><PERSON>, Optional
import random
from scipy.optimize import linear_sum_assignment

from uq_methods.base import BaseUQMethod
from core.nli_shared import get_nli_calculator
from core.embedding_cache import get_embedding_encoder

log = logging.getLogger(__name__)


def sentence_matching(
    text_a: List[str], 
    text_b: List[str], 
    pct_k: float, 
    mode: str,
    embedding_model: str = "intfloat/multilingual-e5-large-instruct"
) -> Tuple[List[str], List[str]]:
    """
    Match sentences between two lists based on embedding similarity.
    
    Args:
        text_a: First list of sentences
        text_b: Second list of sentences  
        pct_k: Percentage of minimum list length to return (0.0 to 1.0)
        mode: Selection mode - "bottom" (most similar), "top" (least similar), or "random"
        embedding_model: Model name for computing embeddings
        
    Returns:
        Tuple of (matched_sentences_from_a, matched_sentences_from_b) with same length
    """
    if not text_a or not text_b:
        return [], []
    
    if not 0.0 <= pct_k <= 1.0:
        raise ValueError("pct_k must be between 0.0 and 1.0")
    
    if mode not in ["bottom", "top", "random"]:
        raise ValueError("mode must be 'bottom', 'top', or 'random'")
    
    # Determine number of matches to return
    min_length = min(len(text_a), len(text_b))
    num_matches = max(1, int(pct_k * min_length))
    
    # Get embedding encoder
    encoder = get_embedding_encoder(embedding_model)
    
    # Compute embeddings for both lists
    embeddings_a = np.array([encoder.encode_one(sent, normalize=True) for sent in text_a])
    embeddings_b = np.array([encoder.encode_one(sent, normalize=True) for sent in text_b])
    
    # Compute distance matrix (1 - cosine similarity)
    similarity_matrix = embeddings_a @ embeddings_b.T
    distance_matrix = 1.0 - similarity_matrix
    
    # Find best matches for the shorter list
    if len(text_a) <= len(text_b):
        shorter_list, longer_list = text_a, text_b
        distance_for_matching = distance_matrix  # rows=shorter, cols=longer
        reverse_order = False
    else:
        shorter_list, longer_list = text_b, text_a
        distance_for_matching = distance_matrix.T  # rows=shorter, cols=longer
        reverse_order = True
    
    # For each sentence in shorter list, find best match in longer list
    matches = []
    used_longer_indices = set()
    
    for i in range(len(shorter_list)):
        # Find available matches (not already used)
        available_distances = []
        for j in range(len(longer_list)):
            if j not in used_longer_indices:
                available_distances.append((distance_for_matching[i, j], i, j))
        
        if available_distances:
            # Sort by distance (ascending = most similar first)
            available_distances.sort(key=lambda x: x[0])
            best_distance, shorter_idx, longer_idx = available_distances[0]
            matches.append((best_distance, shorter_idx, longer_idx))
            used_longer_indices.add(longer_idx)
    
    # Sort matches by distance for selection
    matches.sort(key=lambda x: x[0])
    
    # Select matches based on mode
    if mode == "bottom":  # Most similar (smallest distances)
        selected_matches = matches[:num_matches]
    elif mode == "top":   # Least similar (largest distances)
        selected_matches = matches[-num_matches:]
    else:  # random
        selected_matches = random.sample(matches, min(num_matches, len(matches)))
    
    # Extract matched sentences
    matched_shorter = [shorter_list[match[1]] for match in selected_matches]
    matched_longer = [longer_list[match[2]] for match in selected_matches]
    
    # Return in correct order based on original input
    if reverse_order:
        return matched_longer, matched_shorter
    else:
        return matched_shorter, matched_longer


def sentence_matching_hungarian(
    text_a: List[str], 
    text_b: List[str], 
    pct_k: float, 
    mode: str,
    embedding_model: str = "intfloat/multilingual-e5-large-instruct"
) -> Tuple[List[str], List[str]]:
    """
    使用匈牙利算法进行句子匹配，支持三种筛选策略。
    
    Args:
        text_a: First list of sentences
        text_b: Second list of sentences  
        pct_k: Percentage of minimum list length to return (0.0 to 1.0)
        mode: Selection mode - "bottom" (most similar), "top" (least similar), or "random"
        embedding_model: Model name for computing embeddings
        
    Returns:
        Tuple of (matched_sentences_from_a, matched_sentences_from_b) with same length
    """
    if not text_a or not text_b:
        return [], []
    
    if not 0.0 <= pct_k <= 1.0:
        raise ValueError("pct_k must be between 0.0 and 1.0")
    
    if mode not in ["bottom", "top", "random"]:
        raise ValueError("mode must be 'bottom', 'top', or 'random'")
    
    # Get embedding encoder
    encoder = get_embedding_encoder(embedding_model)
    
    # Compute embeddings for both lists
    embeddings_a = np.array([encoder.encode_one(sent, normalize=True) for sent in text_a])
    embeddings_b = np.array([encoder.encode_one(sent, normalize=True) for sent in text_b])
    
    # Compute similarity matrix and distance matrix
    similarity_matrix = embeddings_a @ embeddings_b.T
    distance_matrix = 1.0 - similarity_matrix
    
    # Use Hungarian algorithm to find optimal assignment
    # For a complete bipartite matching, we need to work with the smaller dimension
    m, n = distance_matrix.shape
    min_dim = min(m, n)
    max_dim = max(m, n)
    
    if m <= n:
        # More sentences in text_b, pad distance matrix
        padded_distance = np.full((min_dim, min_dim), distance_matrix.max() + 1.0)
        padded_distance[:m, :min_dim] = distance_matrix[:, :min_dim]
        cost_matrix = padded_distance
        is_a_shorter = True
    else:
        # More sentences in text_a, transpose and pad
        padded_distance = np.full((min_dim, min_dim), distance_matrix.max() + 1.0)
        padded_distance[:min_dim, :n] = distance_matrix[:min_dim, :]
        cost_matrix = padded_distance
        is_a_shorter = False
    
    # Apply Hungarian algorithm
    row_indices, col_indices = linear_sum_assignment(cost_matrix)
    
    # Extract all valid matches (not padded ones)
    all_matches = []
    for r_idx, c_idx in zip(row_indices, col_indices):
        if is_a_shorter:
            if r_idx < m and c_idx < n:  # Valid match, not padded
                distance = distance_matrix[r_idx, c_idx]
                all_matches.append((distance, r_idx, c_idx, text_a[r_idx], text_b[c_idx]))
        else:
            if r_idx < n and c_idx < m:  # Valid match, not padded
                distance = distance_matrix[c_idx, r_idx]
                all_matches.append((distance, c_idx, r_idx, text_a[c_idx], text_b[r_idx]))
    
    # Sort matches by distance
    all_matches.sort(key=lambda x: x[0])
    
    # Determine number of matches to return
    num_matches = max(1, int(pct_k * min(len(text_a), len(text_b))))
    num_matches = min(num_matches, len(all_matches))
    
    # Select matches based on mode
    if mode == "bottom":  # Most similar (smallest distances)
        selected_matches = all_matches[:num_matches]
    elif mode == "top":   # Least similar (largest distances)
        selected_matches = all_matches[-num_matches:]
    else:  # random
        selected_matches = random.sample(all_matches, num_matches)
    
    # Extract matched sentences
    if is_a_shorter:
        matched_a = [match[3] for match in selected_matches]
        matched_b = [match[4] for match in selected_matches]
        return matched_a, matched_b
    else:
        matched_a = [match[3] for match in selected_matches]
        matched_b = [match[4] for match in selected_matches]
        return matched_a, matched_b


class LUQSENTENCEUQ(BaseUQMethod):
    """
    LUQSENTENCE: Sentence-Level LUQ Uncertainty Quantification
    
    A new UQ method that performs sentence-level matching and NLI computation:
    1. Splits responses into sentences
    2. Uses embedding-based matching to pair similar sentences across responses
    3. Performs NLI on matched sentence pairs instead of sentence-to-full-response
    4. Computes uncertainty based on consistency of matched pairs
    """

    def __init__(
        self,
        nli_model_name: str = "microsoft/deberta-large-mnli",
        embedding_model: str = "intfloat/multilingual-e5-large-instruct",
        pct_k: float = 0.8,
        matching_mode: str = "bottom",
        verbose: bool = False
    ):
        """
        Initialize LUQSENTENCE uncertainty quantification method.

        Args:
            nli_model_name: NLI model name for consistency checking
            embedding_model: Embedding model for sentence matching
            pct_k: Percentage of minimum sentence count to match (0.0 to 1.0)
            matching_mode: "bottom" (most similar), "top" (least similar), or "random"
            verbose: Whether to print debug information
        """
        self.nli_model_name = nli_model_name
        self.embedding_model = embedding_model
        self.pct_k = pct_k
        self.matching_mode = matching_mode
        self.verbose = verbose

        # Initialize NLI calculator for consistency checking
        self.nli_calc = get_nli_calculator(nli_model_name)

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences for LUQSENTENCE analysis.

        Args:
            text: Input text to split

        Returns:
            List of sentences suitable for NLI analysis
        """
        # Clean and preprocess the text first
        text = self._preprocess_text(text)
        if not text:
            return []

        # Use sentence-splitter for sentence segmentation
        sentences = self._nlp_sentence_split(text)

        if sentences and self.verbose:
            log.info(f"Split text ({len(text)} chars) into {len(sentences)} sentences")

        return sentences

    def _preprocess_text(self, text: str) -> str:
        """
        Clean text by removing markdown formatting and noise.

        Args:
            text: Raw input text

        Returns:
            Cleaned text ready for sentence segmentation
        """
        import re

        text = text.strip()
        if not text:
            return ""

        # Remove markdown formatting
        text = re.sub(r'^(#{1,6})\s+(.+)$', r'\2.', text, flags=re.MULTILINE)  # Headers
        text = re.sub(r'^[-=*_]{3,}$', '', text, flags=re.MULTILINE)  # Separators
        text = re.sub(r'\*\*\*(.*?)\*\*\*', r'\1', text)  # Bold italic
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)      # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)          # Italic
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # Links

        # Remove list markers
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # Normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = ' '.join(text.split())

        return text

    def _nlp_sentence_split(self, text: str) -> List[str]:
        """
        Split text into sentences using sentence-splitter library.
        """
        from sentence_splitter import SentenceSplitter
        splitter = SentenceSplitter(language='en')
        sentences = splitter.split(text)
        if self.verbose:
            log.info(f"Used sentence-splitter, found {len(sentences)} sentences")
        return sentences

    def _compute_nli_score(self, premise: str, hypothesis: str) -> float:
        """
        Compute NLI-based entailment probability following LUQ paper approach.

        Args:
            premise: First sentence
            hypothesis: Second sentence

        Returns:
            Entailment probability score
        """
        nli_result = self.nli_calc.compute_nli_scores_cached(premise, hypothesis)

        # LUQ paper: normalize between entailment and contradiction only
        entail_prob = nli_result.entailment
        contradict_prob = nli_result.contradiction

        total_prob = entail_prob + contradict_prob
        if total_prob > 0:
            entail_score = entail_prob / total_prob
        else:
            # If both are 0, default to neutral (0.5)
            entail_score = 0.5

        return entail_score

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LUQSENTENCE uncertainty score for the given responses.

        Args:
            responses: List of response texts to evaluate

        Returns:
            Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LUQSENTENCE computation",
                "method": "LUQSENTENCE"
            }

        try:
            if self.verbose:
                log.info(f"Starting LUQSENTENCE computation for {len(responses)} responses")

            # Split responses into sentences
            sentences_list = []
            for i, response in enumerate(responses):
                sentences = self._split_into_sentences(response)
                sentences_list.append(sentences)
                if self.verbose:
                    log.info(f"Response {i+1} split into {len(sentences)} sentences")

            # Compute detailed LUQSENTENCE scores
            detailed_results = self._compute_luqsentence_detailed(sentences_list)

            return {
                "uncertainty_score": detailed_results["overall_uncertainty"],
                "method": "LUQSENTENCE",
                "num_responses": len(responses),
                "luqsentence_scores_per_sample": detailed_results["luqsentence_scores_per_sample"],
                "consistency_scores_per_sample": detailed_results["consistency_scores_per_sample"],
                "overall_consistency": detailed_results["overall_consistency"],
                "num_sentences_per_response": detailed_results["num_sentences_per_response"],
                "matching_summary": detailed_results["matching_summary"],  # 简化的匹配摘要
                "nli_summary": detailed_results["nli_summary"],  # 简化的NLI摘要
                "metadata": {
                    "nli_model": self.nli_model_name,
                    "embedding_model": self.embedding_model,
                    "pct_k": self.pct_k,
                    "matching_mode": self.matching_mode,
                    "total_sentence_pairs": detailed_results["total_sentence_pairs"],
                    "avg_sentences_per_response": sum(detailed_results["num_sentences_per_response"]) / len(detailed_results["num_sentences_per_response"]),
                    "total_nli_computations": detailed_results["total_nli_computations"]
                }
            }

        except Exception as e:
            log.error(f"Error computing LUQSENTENCE uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LUQSENTENCE"
            }

    def _compute_luqsentence_detailed(self, sentences_list: List[List[str]]) -> Dict[str, Any]:
        """
        Core LUQSENTENCE algorithm implementation.

        For each pair of response samples:
        1. Use sentence matching to find corresponding sentences
        2. Check consistency of matched sentence pairs using NLI
        3. Compute average consistency scores
        4. Convert to uncertainty scores

        Args:
            sentences_list: List of sentence lists for each response

        Returns:
            LUQSENTENCE results with matching and NLI details
        """
        num_samples = len(sentences_list)

        # Initialize result containers
        luqsentence_scores_per_sample = np.zeros(num_samples)
        consistency_scores_per_sample = np.zeros(num_samples)
        num_sentences_per_response = [len(sample) for sample in sentences_list]

        # Summary tracking (simplified for storage efficiency)
        total_sentence_pairs = 0
        total_nli_computations = 0
        matching_stats = []
        nli_score_distribution = []

        for index, sentences in enumerate(sentences_list):
            if self.verbose:
                log.info(f"Processing sample {index+1}/{num_samples} with sentence matching")

            # Get other samples for comparison
            other_samples = [(i, sample) for i, sample in enumerate(sentences_list) if i != index]

            if not other_samples or not sentences:
                # Default values for edge cases
                luqsentence_scores_per_sample[index] = 0.5
                consistency_scores_per_sample[index] = 0.5
                continue

            # Compute consistency scores using sentence matching
            all_scores = []
            sample_total_pairs = 0

            for other_index, other_sentences in other_samples:
                # Perform sentence matching
                matched_current, matched_other = sentence_matching(
                    sentences, other_sentences, self.pct_k, self.matching_mode, self.embedding_model
                )

                if self.verbose:
                    log.info(f"Matched {len(matched_current)} sentence pairs between samples {index+1} and {other_index+1}")

                # Update statistics
                sample_total_pairs += len(matched_current)
                total_sentence_pairs += len(matched_current)

                # Compute NLI scores for matched pairs
                pair_scores = []
                for sent_current, sent_other in zip(matched_current, matched_other):
                    # Compute bidirectional NLI scores
                    nli_forward = self.nli_calc.compute_nli_scores_cached(sent_current, sent_other)
                    nli_backward = self.nli_calc.compute_nli_scores_cached(sent_other, sent_current)

                    # Use LUQSENTENCE scoring method (entailment vs contradiction)
                    score_forward = self._compute_nli_score(sent_current, sent_other)
                    score_backward = self._compute_nli_score(sent_other, sent_current)

                    # Average bidirectional scores
                    avg_score = (score_forward + score_backward) / 2.0
                    pair_scores.append(avg_score)

                    # Collect score for distribution analysis
                    nli_score_distribution.append(avg_score)
                    total_nli_computations += 2  # Forward and backward

                if pair_scores:
                    all_scores.extend(pair_scores)

            # Calculate sample-level metrics
            if all_scores:
                sample_consistency = np.mean(all_scores)
                sample_uncertainty = 1.0 - sample_consistency
            else:
                sample_consistency = 0.5
                sample_uncertainty = 0.5

            consistency_scores_per_sample[index] = sample_consistency
            luqsentence_scores_per_sample[index] = sample_uncertainty

            # Store simplified matching statistics
            matching_stats.append({
                "sample_index": index,
                "num_sentences": len(sentences),
                "num_other_samples": len(other_samples),
                "total_matched_pairs": sample_total_pairs,
                "sample_consistency": round(float(sample_consistency), 4),
                "sample_uncertainty": round(float(sample_uncertainty), 4)
            })

        # Calculate overall metrics
        overall_uncertainty = luqsentence_scores_per_sample.mean()
        overall_consistency = consistency_scores_per_sample.mean()

        # Create simplified summaries
        matching_summary = {
            "total_samples": num_samples,
            "total_sentence_pairs": total_sentence_pairs,
            "avg_pairs_per_sample": total_sentence_pairs / num_samples if num_samples > 0 else 0,
            "sentence_count_stats": {
                "min": min(num_sentences_per_response),
                "max": max(num_sentences_per_response),
                "mean": sum(num_sentences_per_response) / len(num_sentences_per_response),
                "total": sum(num_sentences_per_response)
            }
        }

        nli_summary = {
            "total_nli_computations": total_nli_computations,
            "score_distribution": {
                "min": min(nli_score_distribution) if nli_score_distribution else 0,
                "max": max(nli_score_distribution) if nli_score_distribution else 0,
                "mean": sum(nli_score_distribution) / len(nli_score_distribution) if nli_score_distribution else 0,
                "count": len(nli_score_distribution)
            },
            "consistency_ranges": {
                "high_consistency": sum(1 for s in nli_score_distribution if s > 0.7),
                "medium_consistency": sum(1 for s in nli_score_distribution if 0.4 <= s <= 0.7),
                "low_consistency": sum(1 for s in nli_score_distribution if s < 0.4)
            }
        }

        return {
            "overall_uncertainty": float(overall_uncertainty),
            "luqsentence_scores_per_sample": luqsentence_scores_per_sample.tolist(),
            "consistency_scores_per_sample": consistency_scores_per_sample.tolist(),
            "overall_consistency": float(overall_consistency),
            "num_sentences_per_response": num_sentences_per_response,
            "matching_summary": matching_summary,
            "nli_summary": nli_summary,
            "total_sentence_pairs": total_sentence_pairs,
            "total_nli_computations": total_nli_computations
        }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 2

    def get_method_name(self) -> str:
        """Get the method name."""
        return "LUQSENTENCE"

    def __str__(self):
        return f"LUQSENTENCE(pct_k={self.pct_k}, mode={self.matching_mode})"


class LUQSENTENCEHungarianUQ(BaseUQMethod):
    """
    LUQSENTENCE Hungarian: 使用匈牙利算法的句子级LUQ不确定性量化
    
    这个变体使用匈牙利算法而不是贪心算法进行句子匹配:
    1. 分割响应为句子
    2. 使用匈牙利算法找到最优的句子匹配
    3. 支持三种筛选策略: top pct_k, bottom pct_k, random pct_k
    4. 在匹配的句子对上执行NLI计算
    """

    def __init__(
        self,
        nli_model_name: str = "microsoft/deberta-large-mnli",
        embedding_model: str = "intfloat/multilingual-e5-large-instruct",
        pct_k: float = 0.8,
        matching_mode: str = "bottom",
        verbose: bool = False
    ):
        """
        Initialize LUQSENTENCE Hungarian uncertainty quantification method.

        Args:
            nli_model_name: NLI model name for consistency checking
            embedding_model: Embedding model for sentence matching
            pct_k: Percentage of minimum sentence count to match (0.0 to 1.0)
            matching_mode: "bottom" (most similar), "top" (least similar), or "random"
            verbose: Whether to print debug information
        """
        self.nli_model_name = nli_model_name
        self.embedding_model = embedding_model
        self.pct_k = pct_k
        self.matching_mode = matching_mode
        self.verbose = verbose

        # Initialize NLI calculator for consistency checking
        self.nli_calc = get_nli_calculator(nli_model_name)

    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences for LUQSENTENCE analysis.

        Args:
            text: Input text to split

        Returns:
            List of sentences suitable for NLI analysis
        """
        # Clean and preprocess the text first
        text = self._preprocess_text(text)
        if not text:
            return []

        # Use sentence-splitter for sentence segmentation
        sentences = self._nlp_sentence_split(text)

        if sentences and self.verbose:
            log.info(f"Split text ({len(text)} chars) into {len(sentences)} sentences")

        return sentences

    def _preprocess_text(self, text: str) -> str:
        """
        Clean text by removing markdown formatting and noise.

        Args:
            text: Raw input text

        Returns:
            Cleaned text ready for sentence segmentation
        """
        import re

        text = text.strip()
        if not text:
            return ""

        # Remove markdown formatting
        text = re.sub(r'^(#{1,6})\s+(.+)$', r'\2.', text, flags=re.MULTILINE)  # Headers
        text = re.sub(r'^[-=*_]{3,}$', '', text, flags=re.MULTILINE)  # Separators
        text = re.sub(r'\*\*\*(.*?)\*\*\*', r'\1', text)  # Bold italic
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)      # Bold
        text = re.sub(r'\*(.*?)\*', r'\1', text)          # Italic
        text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # Links

        # Remove list markers
        text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
        text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

        # Normalize whitespace
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)
        text = re.sub(r'[ \t]+', ' ', text)
        text = ' '.join(text.split())

        return text

    def _nlp_sentence_split(self, text: str) -> List[str]:
        """
        Split text into sentences using sentence-splitter library.
        """
        from sentence_splitter import SentenceSplitter
        splitter = SentenceSplitter(language='en')
        sentences = splitter.split(text)
        if self.verbose:
            log.info(f"Used sentence-splitter, found {len(sentences)} sentences")
        return sentences

    def _compute_nli_score(self, premise: str, hypothesis: str) -> float:
        """
        Compute NLI-based entailment probability following LUQ paper approach.

        Args:
            premise: First sentence
            hypothesis: Second sentence

        Returns:
            Entailment probability score
        """
        nli_result = self.nli_calc.compute_nli_scores_cached(premise, hypothesis)

        # LUQ paper: normalize between entailment and contradiction only
        entail_prob = nli_result.entailment
        contradict_prob = nli_result.contradiction

        total_prob = entail_prob + contradict_prob
        if total_prob > 0:
            entail_score = entail_prob / total_prob
        else:
            # If both are 0, default to neutral (0.5)
            entail_score = 0.5

        return entail_score

    def compute_uncertainty(self, responses: List[str]) -> Dict[str, Any]:
        """
        Compute LUQSENTENCE Hungarian uncertainty score for the given responses.

        Args:
            responses: List of response texts to evaluate

        Returns:
            Dictionary containing uncertainty scores and detailed metrics
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 1.0,
                "error": "Need at least 2 responses for LUQSENTENCE computation",
                "method": "LUQSENTENCE_Hungarian"
            }

        try:
            if self.verbose:
                log.info(f"Starting LUQSENTENCE Hungarian computation for {len(responses)} responses")

            # Split responses into sentences
            sentences_list = []
            for i, response in enumerate(responses):
                sentences = self._split_into_sentences(response)
                sentences_list.append(sentences)
                if self.verbose:
                    log.info(f"Response {i+1} split into {len(sentences)} sentences")

            # Compute detailed LUQSENTENCE scores using Hungarian matching
            detailed_results = self._compute_luqsentence_hungarian_detailed(sentences_list)

            return {
                "uncertainty_score": detailed_results["overall_uncertainty"],
                "method": "LUQSENTENCE_Hungarian",
                "num_responses": len(responses),
                "luqsentence_scores_per_sample": detailed_results["luqsentence_scores_per_sample"],
                "consistency_scores_per_sample": detailed_results["consistency_scores_per_sample"],
                "overall_consistency": detailed_results["overall_consistency"],
                "num_sentences_per_response": detailed_results["num_sentences_per_response"],
                "matching_summary": detailed_results["matching_summary"],
                "nli_summary": detailed_results["nli_summary"],
                "metadata": {
                    "nli_model": self.nli_model_name,
                    "embedding_model": self.embedding_model,
                    "pct_k": self.pct_k,
                    "matching_mode": self.matching_mode,
                    "matching_algorithm": "hungarian",
                    "total_sentence_pairs": detailed_results["total_sentence_pairs"],
                    "avg_sentences_per_response": sum(detailed_results["num_sentences_per_response"]) / len(detailed_results["num_sentences_per_response"]),
                    "total_nli_computations": detailed_results["total_nli_computations"]
                }
            }

        except Exception as e:
            log.error(f"Error computing LUQSENTENCE Hungarian uncertainty: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "error": str(e),
                "method": "LUQSENTENCE_Hungarian"
            }

    def _compute_luqsentence_hungarian_detailed(self, sentences_list: List[List[str]]) -> Dict[str, Any]:
        """
        Core LUQSENTENCE Hungarian algorithm implementation.

        For each pair of response samples:
        1. Use Hungarian algorithm sentence matching to find corresponding sentences
        2. Check consistency of matched sentence pairs using NLI
        3. Compute average consistency scores
        4. Convert to uncertainty scores

        Args:
            sentences_list: List of sentence lists for each response

        Returns:
            LUQSENTENCE results with Hungarian matching and NLI details
        """
        num_samples = len(sentences_list)

        # Initialize result containers
        luqsentence_scores_per_sample = np.zeros(num_samples)
        consistency_scores_per_sample = np.zeros(num_samples)
        num_sentences_per_response = [len(sample) for sample in sentences_list]

        # Summary tracking (simplified for storage efficiency)
        total_sentence_pairs = 0
        total_nli_computations = 0
        matching_stats = []
        nli_score_distribution = []

        for index, sentences in enumerate(sentences_list):
            if self.verbose:
                log.info(f"Processing sample {index+1}/{num_samples} with Hungarian sentence matching")

            # Get other samples for comparison
            other_samples = [(i, sample) for i, sample in enumerate(sentences_list) if i != index]

            if not other_samples or not sentences:
                # Default values for edge cases
                luqsentence_scores_per_sample[index] = 0.5
                consistency_scores_per_sample[index] = 0.5
                continue

            # Compute consistency scores using Hungarian sentence matching
            all_scores = []
            sample_total_pairs = 0

            for other_index, other_sentences in other_samples:
                # Perform Hungarian sentence matching
                matched_current, matched_other = sentence_matching_hungarian(
                    sentences, other_sentences, self.pct_k, self.matching_mode, self.embedding_model
                )

                if self.verbose:
                    log.info(f"Hungarian matched {len(matched_current)} sentence pairs between samples {index+1} and {other_index+1}")

                # Update statistics
                sample_total_pairs += len(matched_current)
                total_sentence_pairs += len(matched_current)

                # Compute NLI scores for matched pairs
                pair_scores = []
                for sent_current, sent_other in zip(matched_current, matched_other):
                    # Compute bidirectional NLI scores
                    nli_forward = self.nli_calc.compute_nli_scores_cached(sent_current, sent_other)
                    nli_backward = self.nli_calc.compute_nli_scores_cached(sent_other, sent_current)

                    # Use LUQSENTENCE scoring method (entailment vs contradiction)
                    score_forward = self._compute_nli_score(sent_current, sent_other)
                    score_backward = self._compute_nli_score(sent_other, sent_current)

                    # Average bidirectional scores
                    avg_score = (score_forward + score_backward) / 2.0
                    pair_scores.append(avg_score)

                    # Collect score for distribution analysis
                    nli_score_distribution.append(avg_score)
                    total_nli_computations += 2  # Forward and backward

                if pair_scores:
                    all_scores.extend(pair_scores)

            # Calculate sample-level metrics
            if all_scores:
                sample_consistency = np.mean(all_scores)
                sample_uncertainty = 1.0 - sample_consistency
            else:
                sample_consistency = 0.5
                sample_uncertainty = 0.5

            consistency_scores_per_sample[index] = sample_consistency
            luqsentence_scores_per_sample[index] = sample_uncertainty

            # Store simplified matching statistics
            matching_stats.append({
                "sample_index": index,
                "num_sentences": len(sentences),
                "num_other_samples": len(other_samples),
                "total_matched_pairs": sample_total_pairs,
                "sample_consistency": round(float(sample_consistency), 4),
                "sample_uncertainty": round(float(sample_uncertainty), 4)
            })

        # Calculate overall metrics
        overall_uncertainty = luqsentence_scores_per_sample.mean()
        overall_consistency = consistency_scores_per_sample.mean()

        # Create simplified summaries
        matching_summary = {
            "total_samples": num_samples,
            "total_sentence_pairs": total_sentence_pairs,
            "avg_pairs_per_sample": total_sentence_pairs / num_samples if num_samples > 0 else 0,
            "matching_algorithm": "hungarian",
            "sentence_count_stats": {
                "min": min(num_sentences_per_response),
                "max": max(num_sentences_per_response),
                "mean": sum(num_sentences_per_response) / len(num_sentences_per_response),
                "total": sum(num_sentences_per_response)
            }
        }

        nli_summary = {
            "total_nli_computations": total_nli_computations,
            "score_distribution": {
                "min": min(nli_score_distribution) if nli_score_distribution else 0,
                "max": max(nli_score_distribution) if nli_score_distribution else 0,
                "mean": sum(nli_score_distribution) / len(nli_score_distribution) if nli_score_distribution else 0,
                "count": len(nli_score_distribution)
            },
            "consistency_ranges": {
                "high_consistency": sum(1 for s in nli_score_distribution if s > 0.7),
                "medium_consistency": sum(1 for s in nli_score_distribution if 0.4 <= s <= 0.7),
                "low_consistency": sum(1 for s in nli_score_distribution if s < 0.4)
            }
        }

        return {
            "overall_uncertainty": float(overall_uncertainty),
            "luqsentence_scores_per_sample": luqsentence_scores_per_sample.tolist(),
            "consistency_scores_per_sample": consistency_scores_per_sample.tolist(),
            "overall_consistency": float(overall_consistency),
            "num_sentences_per_response": num_sentences_per_response,
            "matching_summary": matching_summary,
            "nli_summary": nli_summary,
            "total_sentence_pairs": total_sentence_pairs,
            "total_nli_computations": total_nli_computations
        }

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 2

    def get_method_name(self) -> str:
        """Get the method name."""
        return "LUQSENTENCE_Hungarian"

    def __str__(self):
        return f"LUQSENTENCE_Hungarian(pct_k={self.pct_k}, mode={self.matching_mode})"


# 便捷的工厂函数，用于创建不同pct_k值的LUQSENTENCE Hungarian变体
def create_luqsentence_hungarian_variants() -> Dict[str, LUQSENTENCEHungarianUQ]:
    """
    创建不同pct_k值和匹配模式的LUQSENTENCE Hungarian变体
    
    Returns:
        Dictionary mapping variant names to initialized LUQSENTENCE Hungarian instances
    """
    variants = {}
    
    # 不同的pct_k值
    pct_k_values = [0.3, 0.5, 0.7]
    
    # 不同的匹配模式
    modes = ["bottom", "top", "random"]
    
    for pct_k in pct_k_values:
        for mode in modes:
            variant_name = f"LUQSENTENCE_Hungarian_{pct_k}_{mode}"
            variants[variant_name] = LUQSENTENCEHungarianUQ(
                pct_k=pct_k,
                matching_mode=mode,
                verbose=False
            )
    
    return variants
