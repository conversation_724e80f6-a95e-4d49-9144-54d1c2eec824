"""
Brier Score uncertainty quantification methods.
Based on token probability distributions to calculate uncertainty using Brier score methodology.
Brier score measures the accuracy of probabilistic predictions.
"""
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


@dataclass
class TokenBrierData:
    """Token-level Brier score data structure"""
    token: str
    brier_score: float
    position: int
    probability: float
    top_probs: Optional[Dict[str, float]] = None


class BrierScoreUQ(BaseUQMethod):
    """
    Estimates the sequence-level uncertainty using Brier score methodology.
    Works with models that provide token-level probability distributions (logprobs).
    
    Brier score measures the accuracy of probabilistic predictions:
    BS = (1/N) * Σ(p_i - y_i)²
    where p_i is predicted probability, y_i is actual outcome (0 or 1)
    
    Lower Brier scores indicate better calibrated predictions.
    """

    def __init__(self, verbose: bool = False, **kwargs):
        """
        Initialize the Brier score method.
        
        Args:
            verbose: Whether to print debug information
            **kwargs: Additional parameters for Brier score calculation
        """
        self.verbose = verbose
        # Add any specific parameters for Brier score calculation
        self.brier_params = kwargs

    def compute_uncertainty(self, responses: List[str], 
                          responses_with_probs: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Compute BIER score-based uncertainty for responses.
        
        Args:
            responses: List of generated responses
            responses_with_probs: List of responses with probability information
                Each item should contain 'logprobs' field with token probability data
                
        Returns:
            Dictionary containing BIER score uncertainty metrics
        """
        if not responses:
            return {
                "uncertainty_score": 0.0,
                "brier_score": 0.0,
                "num_responses": 0,
                "method": "BrierScore",
                "error": "No responses provided"
            }

        if responses_with_probs is None:
            return {
                "uncertainty_score": 1.0,
                "brier_score": 1.0,
                "num_responses": len(responses),
                "method": "BrierScore",
                "error": "BrierScore requires logprobs data - no fallback available"
            }

        try:
            # Collect Brier scores for each response
            response_brier_scores = []
            token_details = []
            
            for i, response_data in enumerate(responses_with_probs):
                if not isinstance(response_data, dict) or 'logprobs' not in response_data:
                    log.warning(f"Response {i} missing logprobs data, skipping")
                    continue
                
                logprobs_data = response_data['logprobs']
                
                # Extract token-level Brier data
                token_brier_data = self._extract_token_brier_data(logprobs_data, i)
                
                if token_brier_data:
                    # Calculate Brier score for this response
                    response_brier_score = self._calculate_response_brier_score(token_brier_data)
                    response_brier_scores.append(response_brier_score)
                    
                    if self.verbose:
                        log.debug(f"Response {i} Brier score: {response_brier_score:.4f}")
                        token_details.append({
                            "response_id": i,
                            "brier_score": response_brier_score,
                            "token_count": len(token_brier_data),
                            "avg_token_prob": np.mean([td.probability for td in token_brier_data])
                        })

            if not response_brier_scores:
                return {
                    "uncertainty_score": 1.0,
                    "brier_score": 1.0,
                    "num_responses": len(responses),
                    "method": "BrierScore",
                    "error": "No valid probability data found"
                }

            # Calculate overall uncertainty based on Brier scores
            overall_brier_score = self._calculate_overall_uncertainty(response_brier_scores)
            
            # Additional statistics
            brier_mean = np.mean(response_brier_scores)
            brier_std = np.std(response_brier_scores)
            brier_min = np.min(response_brier_scores)
            brier_max = np.max(response_brier_scores)
            
            if self.verbose:
                log.debug(f"Response Brier scores: {[f'{s:.4f}' for s in response_brier_scores]}")
                log.debug(f"Overall uncertainty: {overall_brier_score:.4f}")

            return {
                "uncertainty_score": overall_brier_score,
                "brier_score": overall_brier_score,
                "response_brier_scores": response_brier_scores,
                "brier_statistics": {
                    "mean": brier_mean,
                    "std": brier_std,
                    "min": brier_min,
                    "max": brier_max
                },
                "num_responses": len(responses),
                "num_valid_responses": len(response_brier_scores),
                "method": "BrierScore",
                "metadata": {
                    "token_details": token_details if self.verbose else [],
                    "brier_params": self.brier_params
                }
            }

        except Exception as e:
            log.error(f"Error computing Brier score: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "brier_score": 1.0,
                "num_responses": len(responses),
                "method": "BrierScore",
                "error": str(e)
            }
    
    def _extract_token_brier_data(self, logprobs_data: Dict[str, Any], response_id: int) -> List[TokenBrierData]:
        """
        Extract token-level data needed for BIER score calculation from logprobs data.
        
        Args:
            logprobs_data: Logprobs data structure
            response_id: Response identifier
            
        Returns:
            List of TokenBrierData objects
        """
        token_brier_data = []
        
        try:
            # Handle different logprobs formats (similar to token_entropy.py)
            content = logprobs_data.get("content", [])
            if not content and "tokens" in logprobs_data:
                # Alternative format: direct tokens and probabilities
                tokens = logprobs_data.get("tokens", [])
                top_logprobs = logprobs_data.get("top_logprobs", [])
                
                for pos, (token, token_probs_dict) in enumerate(zip(tokens, top_logprobs)):
                    if isinstance(token_probs_dict, dict) and token_probs_dict:
                        # Get the probability of the actual token
                        token_prob = token_probs_dict.get(token, 0.0)
                        brier_score = self._calculate_token_brier_score(token, token_prob, token_probs_dict)
                        
                        token_brier_data.append(TokenBrierData(
                            token=token,
                            brier_score=brier_score,
                            position=pos,
                            probability=token_prob,
                            top_probs=token_probs_dict
                        ))
            else:
                # VLLM-style format
                for pos, token_data in enumerate(content):
                    if not isinstance(token_data, dict):
                        continue
                    
                    token = token_data.get("token", "")
                    top_logprobs = token_data.get("top_logprobs", [])
                    
                    if top_logprobs:
                        # Convert to probability distribution
                        prob_dict = {}
                        token_prob = 0.0
                        
                        for logprob_item in top_logprobs:
                            if isinstance(logprob_item, dict):
                                tok = logprob_item.get("token", "")
                                logprob = logprob_item.get("logprob", float('-inf'))
                                prob = np.exp(logprob)
                                prob_dict[tok] = prob
                                
                                if tok == token:
                                    token_prob = prob
                        
                        if prob_dict:
                            brier_score = self._calculate_token_brier_score(token, token_prob, prob_dict)
                            token_brier_data.append(TokenBrierData(
                                token=token,
                                brier_score=brier_score,
                                position=pos,
                                probability=token_prob,
                                top_probs=prob_dict
                            ))
        
        except Exception as e:
            log.error(f"Error extracting token Brier data: {str(e)}")
        
        return token_brier_data
    
    def _calculate_token_brier_score(self, token: str, token_prob: float, prob_dict: Dict[str, float]) -> float:
        """
        Calculate Brier score for a single token using self-consistency approach.
        
        For self-consistency Brier score:
        - p_i = predicted probability of the token that was actually generated
        - y_i = 1 (since this token was actually selected)
        - Brier score = (p_i - y_i)² = (p_i - 1)² = (1 - p_i)²
        
        Args:
            token: The actual token that was generated
            token_prob: Probability of the actual token
            prob_dict: Dictionary mapping tokens to their probabilities (unused in this approach)
            
        Returns:
            Brier score value for this token (lower = better calibrated/more confident)
        """
        if token_prob <= 0:
            # If probability is 0 or negative, maximum Brier score
            return 1.0
        
        if token_prob > 1.0:
            # Clamp probability to valid range
            token_prob = 1.0
        
        # Self-consistency Brier score: (1 - p_i)²
        # Lower score = model was more confident about the token it actually generated
        brier_score = (1.0 - token_prob) ** 2
        
        return brier_score
    
    def _calculate_response_brier_score(self, token_brier_data: List[TokenBrierData]) -> float:
        """
        Calculate Brier score for an entire response based on token-level scores.
        
        Uses the mean of token-level Brier scores to get response-level uncertainty.
        
        Args:
            token_brier_data: List of TokenBrierData objects
            
        Returns:
            Response-level Brier score (mean of token-level scores)
        """
        if not token_brier_data:
            return 1.0
        
        # Average token-level Brier scores
        token_scores = [td.brier_score for td in token_brier_data]
        return np.mean(token_scores)
    
    def _calculate_overall_uncertainty(self, response_brier_scores: List[float]) -> float:
        """
        Calculate overall uncertainty from response-level Brier scores.
        
        Uses the mean of response-level Brier scores. Higher scores indicate
        the model was less confident about the tokens it actually generated.
        
        Args:
            response_brier_scores: List of response-level Brier scores
            
        Returns:
            Overall uncertainty measure (mean of response-level Brier scores)
        """
        if not response_brier_scores:
            return 1.0
        
        # Average response-level Brier scores
        return np.mean(response_brier_scores)

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 3  # 可以根据BIER score方法的特点调整

    def get_method_name(self) -> str:
        """Get method name."""
        return "BrierScore"


class TokenBrierScoreUQ(BaseUQMethod):
    """
    Token-level Brier score uncertainty quantification.
    Provides fine-grained, per-token uncertainty analysis using Brier score methodology.
    """

    def __init__(self, verbose: bool = False, **kwargs):
        """
        Initialize the token-level Brier score method.
        
        Args:
            verbose: Whether to print debug information
            **kwargs: Additional parameters for Brier score calculation
        """
        self.verbose = verbose
        self.brier_params = kwargs

    def compute_uncertainty(self, responses: List[str], 
                          responses_with_probs: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Compute token-level BIER score uncertainty for responses.
        
        Returns detailed per-token uncertainty information.
        """
        if not responses:
            return {
                "uncertainty_score": 0.0,
                "token_bier_scores": [],
                "num_responses": 0,
                "method": "TokenBierScore",
                "error": "No responses provided"
            }

        if responses_with_probs is None:
            return {
                "uncertainty_score": 1.0,
                "token_bier_scores": [],
                "num_responses": len(responses),
                "method": "TokenBierScore",
                "error": "TokenBierScore requires logprobs data"
            }

        try:
            # Collect all token BIER scores across responses
            all_token_brier_data = []
            response_token_counts = []
            
            for i, response_data in enumerate(responses_with_probs):
                if not isinstance(response_data, dict) or 'logprobs' not in response_data:
                    log.warning(f"Response {i} missing logprobs data, skipping")
                    continue
                
                logprobs_data = response_data['logprobs']
                
                # Extract token-level BIER data
                token_brier_data = self._extract_token_brier_data(logprobs_data, i)
                
                if token_brier_data:
                    all_token_brier_data.append(token_brier_data)
                    response_token_counts.append(len(token_brier_data))
                    
                    if self.verbose:
                        log.debug(f"Response {i} token count: {len(token_brier_data)}")

            if not all_token_brier_data:
                return {
                    "uncertainty_score": 1.0,
                    "token_bier_scores": [],
                    "num_responses": len(responses),
                    "method": "TokenBierScore",
                    "error": "No valid probability data found"
                }

            # Calculate average uncertainty score across all tokens
            flattened_bier_scores = [td.bier_score for response_data in all_token_brier_data 
                                   for td in response_data]
            
            average_uncertainty = np.mean(flattened_bier_scores) if flattened_bier_scores else 0.0
            
            if self.verbose:
                log.debug(f"Total tokens processed: {len(flattened_bier_scores)}")
                log.debug(f"Average token BIER score: {average_uncertainty:.4f}")

            return {
                "uncertainty_score": average_uncertainty,
                "token_bier_scores": all_token_brier_data,
                "num_responses": len(responses),
                "num_valid_responses": len(all_token_brier_data),
                "total_tokens": len(flattened_bier_scores),
                "avg_tokens_per_response": np.mean(response_token_counts) if response_token_counts else 0,
                "method": "TokenBierScore",
                "metadata": {
                    "response_token_counts": response_token_counts,
                    "bier_score_statistics": {
                        "mean": average_uncertainty,
                        "std": np.std(flattened_bier_scores) if flattened_bier_scores else 0.0,
                        "min": np.min(flattened_bier_scores) if flattened_bier_scores else 0.0,
                        "max": np.max(flattened_bier_scores) if flattened_bier_scores else 0.0,
                        "median": np.median(flattened_bier_scores) if flattened_bier_scores else 0.0
                    },
                    "bier_params": self.bier_params
                }
            }

        except Exception as e:
            log.error(f"Error computing token BIER score: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "token_bier_scores": [],
                "num_responses": len(responses),
                "method": "TokenBierScore",
                "error": str(e)
            }
    
    def _extract_token_brier_data(self, logprobs_data: Dict[str, Any], response_id: int) -> List[TokenBrierData]:
        """
        Extract token-level data needed for BIER score calculation from logprobs data.
        """
        # 重用BrierScoreUQ的实现
        bier_uq = BrierScoreUQ()
        return bier_uq._extract_token_brier_data(logprobs_data, response_id)

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 1  # Token-level analysis can work with single response

    def get_method_name(self) -> str:
        """Get method name."""
        return "TokenBrierScore"


class CrossResponseBrierScoreUQ(BaseUQMethod):
    """
    Advanced Brier score method that uses consensus across multiple responses.
    
    This method calculates Brier score by:
    1. Creating a consensus "ground truth" from multiple responses
    2. Comparing each response against this consensus
    3. Computing uncertainty based on disagreement with consensus
    
    This approach better captures inter-response uncertainty.
    """

    def __init__(self, verbose: bool = False, consensus_method: str = "majority", **kwargs):
        """
        Initialize the cross-response Brier score method.
        
        Args:
            verbose: Whether to print debug information
            consensus_method: How to create consensus ('majority', 'probability_weighted')
            **kwargs: Additional parameters
        """
        self.verbose = verbose
        self.consensus_method = consensus_method
        self.brier_params = kwargs

    def compute_uncertainty(self, responses: List[str], 
                          responses_with_probs: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Compute cross-response Brier score uncertainty.
        
        This method creates a consensus from multiple responses and calculates
        how much each response deviates from this consensus.
        """
        if len(responses) < 2:
            return {
                "uncertainty_score": 0.0,
                "cross_response_brier_score": 0.0,
                "num_responses": len(responses),
                "method": "CrossResponseBrierScore",
                "error": "CrossResponseBrierScore requires at least 2 responses"
            }

        if responses_with_probs is None:
            return {
                "uncertainty_score": 1.0,
                "cross_response_brier_score": 1.0,
                "num_responses": len(responses),
                "method": "CrossResponseBrierScore",
                "error": "CrossResponseBrierScore requires logprobs data"
            }

        try:
            # Step 1: Extract tokens from all responses
            all_response_tokens = []
            
            for i, response_data in enumerate(responses_with_probs):
                if not isinstance(response_data, dict) or 'logprobs' not in response_data:
                    log.warning(f"Response {i} missing logprobs data, skipping")
                    continue
                
                tokens = self._extract_response_tokens(response_data['logprobs'])
                all_response_tokens.append(tokens)
            
            if len(all_response_tokens) < 2:
                return {
                    "uncertainty_score": 1.0,
                    "cross_response_brier_score": 1.0,
                    "num_responses": len(responses),
                    "method": "CrossResponseBrierScore",
                    "error": "Insufficient valid responses with logprobs"
                }

            # Step 2: Create positional consensus
            consensus_data = self._create_positional_consensus(all_response_tokens)
            
            # Step 3: Calculate Brier score for each response against consensus
            response_brier_scores = []
            
            for i, tokens in enumerate(all_response_tokens):
                brier_score = self._calculate_consensus_brier_score(tokens, consensus_data)
                response_brier_scores.append(brier_score)
                
                if self.verbose:
                    log.debug(f"Response {i} consensus Brier score: {brier_score:.4f}")
            
            # Step 4: Calculate overall uncertainty
            overall_uncertainty = np.mean(response_brier_scores)
            consensus_strength = self._calculate_consensus_strength(consensus_data)
            
            return {
                "uncertainty_score": overall_uncertainty,
                "cross_response_brier_score": overall_uncertainty,
                "response_brier_scores": response_brier_scores,
                "consensus_strength": consensus_strength,
                "brier_statistics": {
                    "mean": np.mean(response_brier_scores),
                    "std": np.std(response_brier_scores),
                    "min": np.min(response_brier_scores),
                    "max": np.max(response_brier_scores)
                },
                "num_responses": len(responses),
                "num_valid_responses": len(response_brier_scores),
                "method": "CrossResponseBrierScore",
                "metadata": {
                    "consensus_method": self.consensus_method,
                    "avg_response_length": np.mean([len(tokens) for tokens in all_response_tokens]),
                    "brier_params": self.brier_params
                }
            }

        except Exception as e:
            log.error(f"Error computing cross-response Brier score: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "cross_response_brier_score": 1.0,
                "num_responses": len(responses),
                "method": "CrossResponseBrierScore",
                "error": str(e)
            }

    def _extract_response_tokens(self, logprobs_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract token information from a single response."""
        tokens = []
        
        try:
            content = logprobs_data.get("content", [])
            if not content and "tokens" in logprobs_data:
                # Alternative format
                token_list = logprobs_data.get("tokens", [])
                top_logprobs = logprobs_data.get("top_logprobs", [])
                
                for pos, (token, token_probs_dict) in enumerate(zip(token_list, top_logprobs)):
                    if isinstance(token_probs_dict, dict):
                        token_prob = token_probs_dict.get(token, 0.0)
                        tokens.append({
                            "position": pos,
                            "token": token,
                            "probability": token_prob
                        })
            else:
                # VLLM-style format
                for pos, token_data in enumerate(content):
                    if isinstance(token_data, dict):
                        token = token_data.get("token", "")
                        top_logprobs = token_data.get("top_logprobs", [])
                        
                        # Find token probability
                        token_prob = 0.0
                        for logprob_item in top_logprobs:
                            if isinstance(logprob_item, dict):
                                tok = logprob_item.get("token", "")
                                if tok == token:
                                    logprob = logprob_item.get("logprob", float('-inf'))
                                    token_prob = np.exp(logprob)
                                    break
                        
                        tokens.append({
                            "position": pos,
                            "token": token,
                            "probability": token_prob
                        })
        except Exception as e:
            log.error(f"Error extracting response tokens: {str(e)}")
        
        return tokens

    def _create_positional_consensus(self, all_response_tokens: List[List[Dict[str, Any]]]) -> Dict[int, Dict[str, float]]:
        """
        Create consensus for each position across all responses.
        
        Returns: Dict mapping position -> {token: consensus_probability}
        """
        consensus_data = {}
        
        # Find maximum sequence length
        max_length = max(len(tokens) for tokens in all_response_tokens) if all_response_tokens else 0
        
        for pos in range(max_length):
            position_tokens = []
            
            # Collect all tokens at this position across responses
            for response_tokens in all_response_tokens:
                if pos < len(response_tokens):
                    position_tokens.append(response_tokens[pos]["token"])
            
            if position_tokens:
                # Calculate consensus probability using majority voting
                from collections import Counter
                token_counts = Counter(position_tokens)
                total_count = len(position_tokens)
                
                consensus_data[pos] = {
                    token: count / total_count
                    for token, count in token_counts.items()
                }
        
        return consensus_data

    def _calculate_consensus_brier_score(self, response_tokens: List[Dict[str, Any]], 
                                       consensus_data: Dict[int, Dict[str, float]]) -> float:
        """Calculate Brier score of a response against the consensus."""
        if not response_tokens or not consensus_data:
            return 1.0
        
        total_brier_score = 0.0
        valid_positions = 0
        
        for token_info in response_tokens:
            pos = token_info["position"]
            actual_token = token_info["token"]
            
            if pos in consensus_data:
                consensus_probs = consensus_data[pos]
                
                # For the actual token: y_i = 1, p_i = consensus_prob
                consensus_prob = consensus_probs.get(actual_token, 0.0)
                brier_component = (consensus_prob - 1.0) ** 2
                
                total_brier_score += brier_component
                valid_positions += 1
        
        return total_brier_score / valid_positions if valid_positions > 0 else 1.0

    def _calculate_consensus_strength(self, consensus_data: Dict[int, Dict[str, float]]) -> float:
        """Calculate how strong the consensus is (higher = more agreement)."""
        if not consensus_data:
            return 0.0
        
        position_entropies = []
        
        for pos, token_probs in consensus_data.items():
            if token_probs:
                # Calculate entropy at this position (lower = stronger consensus)
                probs = list(token_probs.values())
                entropy = -sum(p * np.log(p) for p in probs if p > 0)
                position_entropies.append(entropy)
        
        if position_entropies:
            avg_entropy = np.mean(position_entropies)
            # Convert to strength score (0-1, higher = stronger consensus)
            # Maximum possible entropy for position (uniform distribution)
            max_possible_entropy = np.log(5)  # Assume max 5 different tokens per position
            strength = 1.0 - min(avg_entropy / max_possible_entropy, 1.0)
            return max(0.0, strength)
        
        return 0.0

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 5  # Need multiple responses for consensus

    def get_method_name(self) -> str:
        """Get method name."""
        return "CrossResponseBrierScore"
