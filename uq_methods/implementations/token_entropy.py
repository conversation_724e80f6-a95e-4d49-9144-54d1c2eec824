"""
Token-level entropy uncertainty quantification methods.
Based on token probability distributions to calculate uncertainty.
"""
import numpy as np
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from uq_methods.base import BaseUQMethod

log = logging.getLogger(__name__)


@dataclass
class TokenEntropy:
    """Token-level entropy data structure"""
    token: str
    entropy: float
    position: int
    top_probs: Optional[Dict[str, float]] = None


class MeanTokenEntropyUQ(BaseUQMethod):
    """
    Estimates the sequence-level uncertainty of a language model by calculating the
    mean entropy among all tokens in the generation.
    Works with models that provide token-level probability distributions (logprobs).
    
    This method calculates the average entropy across all tokens in a response,
    providing a sequence-level uncertainty measure.
    """

    def __init__(self, verbose: bool = False):
        """
        Initialize the MeanTokenEntropy method.
        
        Args:
            verbose: Whether to print debug information
        """
        self.verbose = verbose

    def compute_uncertainty(self, responses: List[str], 
                          responses_with_probs: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Compute mean token entropy for responses.
        
        Algorithm:
        1. For each response, calculate the mean probability of all tokens
        2. Use these mean probabilities across responses to calculate entropy
        
        Args:
            responses: List of generated responses
            responses_with_probs: List of responses with probability information
                Each item should contain 'logprobs' field with token probability data
                
        Returns:
            Dictionary containing uncertainty metrics
        """
        if not responses:
            return {
                "uncertainty_score": 0.0,
                "mean_token_entropy": 0.0,
                "num_responses": 0,
                "method": "MeanTokenEntropy",
                "error": "No responses provided"
            }

        if responses_with_probs is None:
            return {
                "uncertainty_score": 1.0,
                "mean_token_entropy": 1.0,
                "num_responses": len(responses),
                "method": "MeanTokenEntropy",
                "error": "MeanTokenEntropy requires logprobs data - no fallback available"
            }

        try:
            # Calculate mean token probability for each response
            response_mean_probs = []
            token_details = []
            
            for i, response_data in enumerate(responses_with_probs):
                if not isinstance(response_data, dict) or 'logprobs' not in response_data:
                    log.warning(f"Response {i} missing logprobs data, skipping")
                    continue
                
                logprobs_data = response_data['logprobs']
                
                # Extract token-level probabilities
                token_probs = self._extract_token_probabilities(logprobs_data, i)
                
                if token_probs:
                    # Calculate mean probability for this response
                    response_mean_prob = np.mean(token_probs)
                    response_mean_probs.append(response_mean_prob)
                    
                    if self.verbose:
                        log.debug(f"Response {i} mean probability: {response_mean_prob:.4f}")
                        token_details.append({
                            "response_id": i,
                            "mean_prob": response_mean_prob,
                            "token_count": len(token_probs),
                            "token_probs": token_probs[:10]  # First 10 for debugging
                        })

            if not response_mean_probs:
                return {
                    "uncertainty_score": 1.0,
                    "mean_token_entropy": 1.0,
                    "num_responses": len(responses),
                    "method": "MeanTokenEntropy",
                    "error": "No valid probability data found"
                }

            # Calculate entropy from mean probabilities across responses
            # Normalize the mean probabilities to form a probability distribution
            total_prob = sum(response_mean_probs)
            if total_prob <= 0:
                normalized_probs = [1.0 / len(response_mean_probs)] * len(response_mean_probs)
            else:
                normalized_probs = [p / total_prob for p in response_mean_probs]
            
            # Calculate entropy: H = -Σ(p * log(p))
            entropy = 0.0
            for p in normalized_probs:
                if p > 0:
                    entropy -= p * np.log(p)
            
            # Additional statistics
            prob_mean = np.mean(response_mean_probs)
            prob_std = np.std(response_mean_probs)
            prob_min = np.min(response_mean_probs)
            prob_max = np.max(response_mean_probs)
            
            if self.verbose:
                log.debug(f"Response mean probabilities: {[f'{p:.4f}' for p in response_mean_probs]}")
                log.debug(f"Calculated entropy: {entropy:.4f}")

            return {
                "uncertainty_score": entropy,
                "mean_token_entropy": entropy,
                "response_mean_probs": response_mean_probs,
                "prob_statistics": {
                    "mean": prob_mean,
                    "std": prob_std,
                    "min": prob_min,
                    "max": prob_max
                },
                "num_responses": len(responses),
                "num_valid_responses": len(response_mean_probs),
                "method": "MeanTokenEntropy",
                "metadata": {
                    "token_details": token_details if self.verbose else []
                }
            }

        except Exception as e:
            log.error(f"Error computing mean token entropy: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "mean_token_entropy": 1.0,
                "num_responses": len(responses),
                "method": "MeanTokenEntropy",
                "error": str(e)
            }
    
    def _extract_token_probabilities(self, logprobs_data: Dict[str, Any], response_id: int) -> List[float]:
        """
        Extract token-level probabilities (not entropies) from logprobs data.
        Optimized for VLLM sentiment analysis format.

        Args:
            logprobs_data: Logprobs data structure
            response_id: Response identifier

        Returns:
            List of token probabilities (highest probability for each token)
        """
        token_probs = []

        try:
            # Handle different logprobs formats
            content = logprobs_data.get("content", [])

            if not content and "tokens" in logprobs_data:
                # Alternative format: direct tokens and probabilities
                tokens = logprobs_data.get("tokens", [])
                top_logprobs = logprobs_data.get("top_logprobs", [])

                for pos, (token, token_probs_dict) in enumerate(zip(tokens, top_logprobs)):
                    if isinstance(token_probs_dict, dict) and token_probs_dict:
                        # Get the maximum probability (most likely token)
                        max_prob = max(token_probs_dict.values()) if token_probs_dict else 0.0
                        token_probs.append(max_prob)
            else:
                # VLLM-style format (主要用于sentiment analysis)
                for pos, token_data in enumerate(content):
                    if not isinstance(token_data, dict):
                        continue

                    token = token_data.get("token", "")
                    logprob = token_data.get("logprob", float('-inf'))
                    top_logprobs = token_data.get("top_logprobs", [])

                    # 优先使用直接的logprob值（VLLM格式）
                    if logprob != float('-inf'):
                        token_prob = np.exp(logprob)
                        token_probs.append(token_prob)
                    elif top_logprobs:
                        # 备用：从top_logprobs中查找
                        token_prob = 0.0
                        for logprob_item in top_logprobs:
                            if isinstance(logprob_item, dict):
                                item_token = logprob_item.get("token", "")
                                if item_token == token:  # Find the actual token's probability
                                    item_logprob = logprob_item.get("logprob", float('-inf'))
                                    token_prob = np.exp(item_logprob)
                                    break

                        # If token not found in top_logprobs, use the first (highest) probability
                        if token_prob == 0.0 and top_logprobs:
                            first_item = top_logprobs[0]
                            if isinstance(first_item, dict):
                                item_logprob = first_item.get("logprob", float('-inf'))
                                token_prob = np.exp(item_logprob)

                        token_probs.append(token_prob)
                    else:
                        # 如果没有概率信息，跳过这个token
                        log.warning(f"No probability information for token '{token}' at position {pos}")

        except Exception as e:
            log.error(f"Error extracting token probabilities: {str(e)}")

        return token_probs

    def _extract_token_entropies(self, logprobs_data: Dict[str, Any], response_id: int) -> List[TokenEntropy]:
        """
        Extract token-level entropy from logprobs data.
        Optimized for VLLM sentiment analysis format.

        Args:
            logprobs_data: Logprobs data structure
            response_id: Response identifier (for debugging)

        Returns:
            List of TokenEntropy objects
        """
        token_entropies = []

        try:
            # Handle different logprobs formats
            content = logprobs_data.get("content", [])

            if not content and "tokens" in logprobs_data:
                # Alternative format: direct tokens and probabilities
                tokens = logprobs_data.get("tokens", [])
                top_logprobs = logprobs_data.get("top_logprobs", [])

                for pos, (token, token_probs) in enumerate(zip(tokens, top_logprobs)):
                    if isinstance(token_probs, dict) and token_probs:
                        entropy = self._calculate_token_entropy(token_probs)
                        token_entropies.append(TokenEntropy(
                            token=token,
                            entropy=entropy,
                            position=pos,
                            top_probs=token_probs
                        ))
            else:
                # VLLM-style format (主要用于sentiment analysis)
                for pos, token_data in enumerate(content):
                    if not isinstance(token_data, dict):
                        continue

                    token = token_data.get("token", "")
                    top_logprobs = token_data.get("top_logprobs", [])

                    # 处理top_logprobs数据
                    if top_logprobs:
                        # Convert to probability distribution
                        prob_dict = {}
                        for logprob_item in top_logprobs:
                            if isinstance(logprob_item, dict):
                                tok = logprob_item.get("token", "")
                                logprob = logprob_item.get("logprob", float('-inf'))
                                if logprob != float('-inf'):
                                    prob_dict[tok] = np.exp(logprob)

                        if prob_dict:
                            entropy = self._calculate_token_entropy(prob_dict)
                            token_entropies.append(TokenEntropy(
                                token=token,
                                entropy=entropy,
                                position=pos,
                                top_probs=prob_dict
                            ))
                    else:
                        # 如果没有top_logprobs，尝试使用单个logprob值
                        logprob = token_data.get("logprob", float('-inf'))
                        if logprob != float('-inf'):
                            # 单个概率值，熵为0（完全确定）
                            prob = np.exp(logprob)
                            token_entropies.append(TokenEntropy(
                                token=token,
                                entropy=0.0,  # 单个概率值的熵为0
                                position=pos,
                                top_probs={token: prob}
                            ))
                        else:
                            log.warning(f"No probability information for token '{token}' at position {pos} in response {response_id}")

        except Exception as e:
            log.error(f"Error extracting token entropies for response {response_id}: {str(e)}")

        return token_entropies
    
    def _calculate_token_entropy(self, prob_dict: Dict[str, float]) -> float:
        """
        Calculate entropy for a single token's probability distribution.
        
        Args:
            prob_dict: Dictionary mapping tokens to their probabilities
            
        Returns:
            Entropy value (higher = more uncertain)
        """
        if not prob_dict:
            return 0.0
        
        # Normalize probabilities to ensure they sum to 1
        total_prob = sum(prob_dict.values())
        if total_prob <= 0:
            return 0.0
        
        normalized_probs = [p / total_prob for p in prob_dict.values()]
        
        # Calculate entropy: H = -Σ(p * log(p))
        entropy = 0.0
        for p in normalized_probs:
            if p > 0:
                entropy -= p * np.log(p)
        
        return entropy



    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 3

    def get_method_name(self) -> str:
        """Get method name."""
        return "MeanTokenEntropy"


class TokenEntropyUQ(BaseUQMethod):
    """
    Estimates the token-level uncertainty of a language model by calculating the
    entropy for each token in the generation.
    Works with models that provide token-level probability distributions (logprobs).
    
    This method returns per-token uncertainty values, useful for fine-grained
    uncertainty analysis.
    """

    def __init__(self, verbose: bool = False):
        """
        Initialize the TokenEntropy method.
        
        Args:
            verbose: Whether to print debug information
        """
        self.verbose = verbose

    def compute_uncertainty(self, responses: List[str], 
                          responses_with_probs: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Compute token-level entropy for responses.
        
        Args:
            responses: List of generated responses
            responses_with_probs: List of responses with probability information
                
        Returns:
            Dictionary containing token-level uncertainty metrics
        """
        if not responses:
            return {
                "uncertainty_score": 0.0,
                "token_entropies": [],
                "num_responses": 0,
                "method": "TokenEntropy",
                "error": "No responses provided"
            }

        if responses_with_probs is None:
            return {
                "uncertainty_score": 1.0,
                "token_entropies": [],
                "num_responses": len(responses),
                "method": "TokenEntropy",
                "error": "TokenEntropy requires logprobs data - no fallback available"
            }

        try:
            
            # Collect all token entropies across responses
            all_token_entropies = []
            response_token_counts = []
            
            for i, response_data in enumerate(responses_with_probs):
                if not isinstance(response_data, dict) or 'logprobs' not in response_data:
                    log.warning(f"Response {i} missing logprobs data, skipping")
                    continue
                
                logprobs_data = response_data['logprobs']
                
                # Extract token-level entropies  
                token_entropies = self._extract_token_entropies(logprobs_data, i)
                
                if token_entropies:
                    # Store entropies for this response (excluding last token as per reference)
                    response_entropies = [te.entropy for te in token_entropies[:-1]] if len(token_entropies) > 1 else [te.entropy for te in token_entropies]
                    all_token_entropies.append(response_entropies)
                    response_token_counts.append(len(response_entropies))
                    
                    if self.verbose:
                        log.debug(f"Response {i} token count: {len(response_entropies)}")

            if not all_token_entropies:
                return {
                    "uncertainty_score": 1.0,
                    "token_entropies": [],
                    "num_responses": len(responses),
                    "method": "TokenEntropy",
                    "error": "No valid probability data found"
                }

            # Calculate average uncertainty score across all tokens
            flattened_entropies = [entropy for response_entropies in all_token_entropies 
                                 for entropy in response_entropies]
            
            average_uncertainty = np.mean(flattened_entropies) if flattened_entropies else 0.0
            
            if self.verbose:
                log.debug(f"Total tokens processed: {len(flattened_entropies)}")
                log.debug(f"Average token entropy: {average_uncertainty:.4f}")

            return {
                "uncertainty_score": average_uncertainty,
                "token_entropies": all_token_entropies,
                "num_responses": len(responses),
                "num_valid_responses": len(all_token_entropies),
                "total_tokens": len(flattened_entropies),
                "avg_tokens_per_response": np.mean(response_token_counts) if response_token_counts else 0,
                "method": "TokenEntropy",
                "metadata": {
                    "response_token_counts": response_token_counts,
                    "entropy_statistics": {
                        "mean": average_uncertainty,
                        "std": np.std(flattened_entropies) if flattened_entropies else 0.0,
                        "min": np.min(flattened_entropies) if flattened_entropies else 0.0,
                        "max": np.max(flattened_entropies) if flattened_entropies else 0.0,
                        "median": np.median(flattened_entropies) if flattened_entropies else 0.0
                    }
                }
            }

        except Exception as e:
            log.error(f"Error computing token entropy: {str(e)}")
            return {
                "uncertainty_score": 1.0,
                "token_entropies": [],
                "num_responses": len(responses),
                "method": "TokenEntropy",
                "error": str(e)
            }
    
    def _extract_token_entropies(self, logprobs_data: Dict[str, Any], response_id: int) -> List[TokenEntropy]:
        """
        Extract token-level entropy from logprobs data.
        Optimized for VLLM sentiment analysis format.

        Args:
            logprobs_data: Logprobs data structure
            response_id: Response identifier (for debugging)

        Returns:
            List of TokenEntropy objects
        """
        token_entropies = []

        try:
            # Handle different logprobs formats
            content = logprobs_data.get("content", [])

            if not content and "tokens" in logprobs_data:
                # Alternative format: direct tokens and probabilities
                tokens = logprobs_data.get("tokens", [])
                top_logprobs = logprobs_data.get("top_logprobs", [])

                for pos, (token, token_probs) in enumerate(zip(tokens, top_logprobs)):
                    if isinstance(token_probs, dict) and token_probs:
                        entropy = self._calculate_token_entropy(token_probs)
                        token_entropies.append(TokenEntropy(
                            token=token,
                            entropy=entropy,
                            position=pos,
                            top_probs=token_probs
                        ))
            else:
                # VLLM-style format (主要用于sentiment analysis)
                for pos, token_data in enumerate(content):
                    if not isinstance(token_data, dict):
                        continue

                    token = token_data.get("token", "")
                    top_logprobs = token_data.get("top_logprobs", [])

                    # 处理top_logprobs数据
                    if top_logprobs:
                        # Convert to probability distribution
                        prob_dict = {}
                        for logprob_item in top_logprobs:
                            if isinstance(logprob_item, dict):
                                tok = logprob_item.get("token", "")
                                logprob = logprob_item.get("logprob", float('-inf'))
                                if logprob != float('-inf'):
                                    prob_dict[tok] = np.exp(logprob)

                        if prob_dict:
                            entropy = self._calculate_token_entropy(prob_dict)
                            token_entropies.append(TokenEntropy(
                                token=token,
                                entropy=entropy,
                                position=pos,
                                top_probs=prob_dict
                            ))
                    else:
                        # 如果没有top_logprobs，尝试使用单个logprob值
                        logprob = token_data.get("logprob", float('-inf'))
                        if logprob != float('-inf'):
                            # 单个概率值，熵为0（完全确定）
                            prob = np.exp(logprob)
                            token_entropies.append(TokenEntropy(
                                token=token,
                                entropy=0.0,  # 单个概率值的熵为0
                                position=pos,
                                top_probs={token: prob}
                            ))
                        else:
                            log.warning(f"No probability information for token '{token}' at position {pos} in response {response_id}")

        except Exception as e:
            log.error(f"Error extracting token entropies for response {response_id}: {str(e)}")

        return token_entropies
    
    def _calculate_token_entropy(self, prob_dict: Dict[str, float]) -> float:
        """
        Calculate entropy for a single token's probability distribution.
        
        Args:
            prob_dict: Dictionary mapping tokens to their probabilities
            
        Returns:
            Entropy value (higher = more uncertain)
        """
        if not prob_dict:
            return 0.0
        
        # Normalize probabilities to ensure they sum to 1
        total_prob = sum(prob_dict.values())
        if total_prob <= 0:
            return 0.0
        
        normalized_probs = [p / total_prob for p in prob_dict.values()]
        
        # Calculate entropy: H = -Σ(p * log(p))
        entropy = 0.0
        for p in normalized_probs:
            if p > 0:
                entropy -= p * np.log(p)
        
        return entropy

    def get_required_samples(self) -> int:
        """Return the number of samples required for this method."""
        return 1

    def get_method_name(self) -> str:
        """Get method name."""
        return "TokenEntropy"
