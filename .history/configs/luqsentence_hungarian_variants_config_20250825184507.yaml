# LUQSENTENCE Hungarian Variants Analysis Configuration
# 专门用于LUQSENTENCE Hungarian变体方法的counterfactual数据分析

# MongoDB Configuration
mongodb:
  host: "localhost"
  port: 27017
  database: "LLM-UQ"
  source_collection: "response_collections"

# Analysis Configuration
analysis:
  # Test mode: process limited records for testing
  test_mode: false
  test_limit: 10

  # LLM model being analyzed
  llm_model: "qwen3-32b"

  # Batch processing
  batch_size: 20

  # Progress reporting
  progress_report_interval: 5

# Tasks to analyze - 只启用counterfactual分析
tasks:
  counterfactual_qa:
    enabled: true
    dataset_sources:
      - "counterfactual_data"
    output_collection: "UQ_result_LUQSENTENCE_Hungarian_variants"  # 新的collection名称
    response_field: "parsed_answer"

# UQ Methods Configuration - 启用所有LUQSENTENCE Hungarian变体
uq_methods:
  # 启用所有Hungarian变体方法
  enabled_methods:
    - "LUQSENTENCE_Hungarian_0.3_bottom"
    - "LUQSENTENCE_Hungarian_0.3_top"
    - "LUQSENTENCE_Hungarian_0.3_random"
    - "LUQSENTENCE_Hungarian_0.5_bottom"
    - "LUQSENTENCE_Hungarian_0.5_top"
    - "LUQSENTENCE_Hungarian_0.5_random"
    - "LUQSENTENCE_Hungarian_0.7_bottom"
    - "LUQSENTENCE_Hungarian_0.7_top"
    - "LUQSENTENCE_Hungarian_0.7_random"
  
  # Method-specific parameters for different variants
  method_params:
    LUQSENTENCE_Hungarian_0.3_bottom:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.3
      matching_mode: "bottom"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.3_top:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.3
      matching_mode: "top"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.3_random:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.3
      matching_mode: "random"
      verbose: false

    LUQSENTENCE_Hungarian_0.5_bottom:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.5
      matching_mode: "bottom"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.5_top:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.5
      matching_mode: "top"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.5_random:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.5
      matching_mode: "random"
      verbose: false

    LUQSENTENCE_Hungarian_0.7_bottom:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.7
      matching_mode: "bottom"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.7_top:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.7
      matching_mode: "top"
      verbose: false
    
    LUQSENTENCE_Hungarian_0.7_random:
      nli_model_name: "microsoft/deberta-large-mnli"
      embedding_model: "intfloat/multilingual-e5-large-instruct"
      pct_k: 0.7
      matching_mode: "random"
      verbose: false

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/luqsentence_hungarian_variants.log"

# Resume Configuration
resume:
  enabled: true
  checkpoint_interval: 10  # Save progress every 10 groups
  resume_file: "checkpoints/luqsentence_hungarian_variants_resume.json"

# Output Configuration
output:
  # Save detailed results for analysis
  save_detailed_results: true
  # Create comparison report
  create_comparison_report: true
  # Export results to CSV
  export_csv: true
  csv_file: "results/luqsentence_hungarian_variants_results.csv"
