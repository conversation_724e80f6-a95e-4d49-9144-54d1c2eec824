#!/usr/bin/env python3
"""
基于VLLM的情感分析响应生成器
将原有的API调用改为本地VLLM运行，并返回logprob信息
"""

import os
import json
import logging
import requests
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import numpy as np
from pathlib import Path
import yaml
import re
import random

# 导入原有的prompt管理器
from prompts.json_prompt_manager import JSONPromptManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# VLLM API配置表 - 可以通过修改这里来调整API接口
VLLM_API_CONFIG = {
    # API类型: 'vllm_generate' 使用/generate接口, 'vllm_chat' 使用/v1/chat/completions接口
    'api_type': 'vllm_chat',  # 改为使用chat接口，支持logprobs

    # API端点配置
    'endpoints': {
        'generate': '/generate',
        'chat_completions': '/v1/chat/completions',
        'models': '/v1/models'
    },

    # logprobs支持配置
    'logprobs_support': {
        'vllm_generate': False,  # 当前VLLM generate接口不支持logprobs
        'vllm_chat': True        # Chat接口通常支持logprobs
    },

    # 请求参数映射配置
    'parameter_mapping': {
        'vllm_generate': {
            'prompt_key': 'prompt',
            'logprobs_key': 'logprobs',
            'logprobs_is_boolean': False,  # VLLM generate接口logprobs是数字
            'response_text_path': ['text', 0],  # 响应中文本的路径
            'response_logprobs_path': ['logprobs']
        },
        'vllm_chat': {
            'prompt_key': 'messages',
            'logprobs_key': 'logprobs',
            'logprobs_is_boolean': True,  # Chat接口logprobs是布尔值
            'response_text_path': ['choices', 0, 'message', 'content'],
            'response_logprobs_path': ['choices', 0, 'logprobs']
        }
    }
}


@dataclass
class VLLMResponse:
    """VLLM响应数据结构"""
    content: str
    logprobs: Optional[Dict[str, Any]] = None
    model: Optional[str] = None
    finish_reason: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None


class VLLMSentimentGenerator:
    """基于VLLM的情感分析响应生成器"""
    
    def __init__(self,
                 vllm_host: str = "http://localhost:8000",
                 api_key: str = "token-abc123",
                 model_name: str = "LLM-Research/Meta-Llama-3.1-8B-Instruct",
                 config_path: Optional[str] = None):
        """
        初始化VLLM情感分析生成器
        
        Args:
            vllm_host: VLLM服务地址
            api_key: API密钥
            model_name: 模型名称
            config_path: 配置文件路径
        """
        self.vllm_host = vllm_host
        self.api_key = api_key
        self.model_name = model_name
        
        # 加载配置
        self.config = self._load_config(config_path)

        # 初始化prompt管理器
        self.prompt_manager = JSONPromptManager()

        # 初始化MongoDB连接（如果配置了）
        self._init_mongodb()

        # 验证VLLM服务
        self._verify_vllm_service()

        logger.info(f"Initialized VLLM Sentiment Generator with model: {model_name}")

    def _load_config(self, config_path: Optional[str]) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded config from: {config_path}")
            return config
        else:
            # 默认配置，遵循原有config.yaml格式
            # 使用全局配置表
            default_config = {
                'model': {
                    'name': 'LLM-Research/Meta-Llama-3.1-8B-Instruct',
                    'base_url': 'http://localhost:8000',
                    'api_key': 'token-abc123',
                    'temperature': 0.7,
                    'top_p': 0.95,
                    'max_tokens': 1500,
                    'enable_thinking': False,
                    'enable_logprobs': True,
                    'top_logprobs': 0,
                    'stream': False,
                    # 从全局配置表获取API配置
                    'api_type': VLLM_API_CONFIG['api_type'],
                    'endpoints': VLLM_API_CONFIG['endpoints'].copy(),
                    'parameter_mapping': VLLM_API_CONFIG['parameter_mapping'].copy()
                },
                'output': {
                    'format': 'mongodb',
                    'mongo': {
                        'host': 'localhost',
                        'port': 27017,
                        'database': 'LLM-UQ',
                        'collection': 'response_collection_no_thinking'
                    }
                },
                'tasks': {
                    'sentiment_analysis': {
                        'enabled': True,
                        'name': 'sentiment_analysis',
                        'task_category': 'sentiment_analysis',
                        'dataset_source': 'twitter_sentiment',
                        'sample_prompts': 5,
                        'attempts_per_prompt': 6,
                        'template_variable': 'tweet',
                        'id_field': 'id',
                        'text_field': 'text',
                        'label_field': 'label',
                        'data_file': 'data/sampled_semeval.csv',
                        'max_samples': None
                    }
                }
            }
            logger.info("Using default configuration")
            return default_config
    
    def _verify_vllm_service(self):
        """验证VLLM服务是否可用"""
        try:
            model_config = self.config.get('model', {})
            api_type = model_config.get('api_type', 'vllm_generate')
            endpoints = model_config.get('endpoints', {})

            if api_type == 'vllm_generate':
                # 使用/generate端点测试服务可用性
                test_payload = {
                    "prompt": "Hello",
                    "max_tokens": 1,
                    "temperature": 0.1
                }
                endpoint = endpoints.get('generate', '/generate')

                response = requests.post(
                    f"{self.vllm_host}{endpoint}",
                    json=test_payload,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )

            elif api_type in ['openai_chat', 'vllm_chat']:
                # 使用chat/completions端点测试服务可用性
                test_payload = {
                    "model": self.model_name,
                    "messages": [{"role": "user", "content": "Hello"}],
                    "max_tokens": 1,
                    "temperature": 0.1
                }
                endpoint = endpoints.get('chat_completions', '/v1/chat/completions')

                headers = {"Content-Type": "application/json"}
                if self.api_key and self.api_key != 'token-abc123':
                    headers["Authorization"] = f"Bearer {self.api_key}"

                response = requests.post(
                    f"{self.vllm_host}{endpoint}",
                    json=test_payload,
                    headers=headers,
                    timeout=30
                )
            else:
                raise ValueError(f"Unsupported api_type: {api_type}")

            if response.status_code == 200:
                logger.info(f"VLLM service is available at {self.vllm_host}")
                logger.info(f"Using model: {self.model_name}")
                logger.info(f"API type: {api_type}")
            else:
                logger.error(f"Failed to connect to VLLM at {self.vllm_host}: {response.status_code}")
                logger.error(f"Response: {response.text}")
                raise ConnectionError(f"VLLM service not available")

        except Exception as e:
            logger.error(f"Error verifying VLLM service: {e}")
            raise
    
    def call_vllm_with_logprobs(self, prompt: str, **kwargs) -> VLLMResponse:
        """
        调用VLLM API并获取logprobs，根据配置表选择API接口

        Args:
            prompt: 输入提示词
            **kwargs: 额外参数

        Returns:
            VLLMResponse: 包含内容和logprobs的响应
        """
        model_config = self.config.get('model', {})
        api_type = model_config.get('api_type', VLLM_API_CONFIG['api_type'])
        endpoints = model_config.get('endpoints', VLLM_API_CONFIG['endpoints'])
        parameter_mapping = model_config.get('parameter_mapping', VLLM_API_CONFIG['parameter_mapping'])

        # 对于qwen模型，如果禁用thinking，在prompt后添加/nothink指令
        final_prompt = prompt
        if self.model_name.lower().startswith('qwen') and not model_config.get('enable_thinking', False):
            final_prompt = prompt + "\n\n/nothink"
            logger.debug("Added /nothink instruction to disable thinking mode")

        return self._call_vllm_api(final_prompt, api_type, model_config, endpoints, parameter_mapping, **kwargs)

    def _call_vllm_api(self, prompt: str, api_type: str, model_config: Dict[str, Any],
                       endpoints: Dict[str, str], parameter_mapping: Dict[str, Any], **kwargs) -> VLLMResponse:
        """统一的VLLM API调用方法，根据配置选择接口"""

        if api_type == 'vllm_generate':
            return self._call_vllm_generate(prompt, model_config, endpoints, parameter_mapping, **kwargs)
        elif api_type == 'vllm_chat':
            return self._call_vllm_chat(prompt, model_config, endpoints, parameter_mapping, **kwargs)
        else:
            raise ValueError(f"Unsupported api_type: {api_type}")

    def _call_vllm_generate(self, prompt: str, model_config: Dict[str, Any], endpoints: Dict[str, str],
                           parameter_mapping: Dict[str, Any], **kwargs) -> VLLMResponse:
        """使用/generate接口调用VLLM"""
        # 构建请求参数
        payload = {
            "prompt": prompt,
            "max_tokens": kwargs.get('max_tokens', model_config.get('max_tokens', 512)),
            "temperature": kwargs.get('temperature', model_config.get('temperature', 0.7)),
            "top_p": kwargs.get('top_p', model_config.get('top_p', 0.95)),
        }

        # 检查当前API类型是否支持logprobs
        logprobs_support = VLLM_API_CONFIG.get('logprobs_support', {})
        api_supports_logprobs = logprobs_support.get('vllm_generate', False)

        if model_config.get('enable_logprobs', True) and api_supports_logprobs:
            payload["logprobs"] = model_config.get('top_logprobs', 0)  # VLLM的logprobs参数是数字
            logger.debug("Added logprobs parameter to request")
        else:
            logger.debug("Logprobs not supported or disabled for vllm_generate API")

        try:
            endpoint = endpoints.get('generate', '/generate')
            logger.debug(f"Sending payload to VLLM generate endpoint: {payload}")

            response = requests.post(
                f"{self.vllm_host}{endpoint}",
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()

                # VLLM generate接口返回格式: {"text": ["generated_text"]}
                if result.get("text") and isinstance(result["text"], list):
                    content = result["text"][0].strip()

                    # 提取logprobs信息（如果有）
                    logprobs = result.get("logprobs")

                    logger.debug(f"VLLM generate response - Content: {content[:100]}...")

                    return VLLMResponse(
                        content=content,
                        logprobs=logprobs,
                        model=self.model_name,
                        finish_reason="stop",
                        usage=result.get("usage")
                    )
                else:
                    logger.warning("Invalid response format from VLLM generate endpoint")
                    return VLLMResponse(content="", model=self.model_name)
            else:
                logger.error(f"VLLM generate API error: {response.status_code} - {response.text}")
                return VLLMResponse(content="", model=self.model_name)

        except Exception as e:
            logger.error(f"Error calling VLLM generate API: {e}")
            return VLLMResponse(content="", model=self.model_name)

    def _call_vllm_chat(self, prompt: str, model_config: Dict[str, Any], endpoints: Dict[str, str],
                       parameter_mapping: Dict[str, Any], **kwargs) -> VLLMResponse:
        """使用chat/completions接口调用VLLM"""
        # 构建请求参数
        payload = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": kwargs.get('max_tokens', model_config.get('max_tokens', 512)),
            "temperature": kwargs.get('temperature', model_config.get('temperature', 0.7)),
            "top_p": kwargs.get('top_p', model_config.get('top_p', 0.95)),
        }

        # 检查模型是否支持logprobs
        if model_config.get('enable_logprobs', True):
            payload["logprobs"] = True
            payload["top_logprobs"] = model_config.get('top_logprobs', 0)

        # 对于qwen模型，设置enable_thinking参数
        if self.model_name.lower().startswith('qwen'):
            enable_thinking = model_config.get('enable_thinking', False)
            payload["chat_template_kwargs"] = {"enable_thinking": enable_thinking}

        try:
            endpoint = endpoints.get('chat_completions', '/v1/chat/completions')
            logger.debug(f"Sending payload to VLLM chat endpoint: {payload}")

            headers = {"Content-Type": "application/json"}
            if self.api_key and self.api_key != 'token-abc123':
                headers["Authorization"] = f"Bearer {self.api_key}"

            response = requests.post(
                f"{self.vllm_host}{endpoint}",
                json=payload,
                headers=headers,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()

                if result.get("choices"):
                    choice = result["choices"][0]
                    message = choice.get("message", {})
                    content = message.get("content", "").strip()
                    logprobs = choice.get("logprobs")
                    finish_reason = choice.get("finish_reason")
                    usage = result.get("usage")

                    logger.debug(f"VLLM chat response - Content: {content[:100]}...")
                    logger.debug(f"VLLM chat response - Finish reason: {finish_reason}")

                    return VLLMResponse(
                        content=content,
                        logprobs=logprobs,
                        model=self.model_name,
                        finish_reason=finish_reason,
                        usage=usage
                    )
                else:
                    logger.warning("No choices in VLLM chat response")
                    return VLLMResponse(content="", model=self.model_name)
            else:
                logger.error(f"VLLM chat API error: {response.status_code} - {response.text}")
                return VLLMResponse(content="", model=self.model_name)

        except Exception as e:
            logger.error(f"Error calling VLLM chat API: {e}")
            return VLLMResponse(content="", model=self.model_name)

    def extract_thinking_content(self, raw_response: str) -> Dict[str, str]:
        """提取thinking内容，与原有代码保持一致"""
        if not raw_response:
            return {'thinking': '', 'response': raw_response}

        # 查找thinking标签（支持<think>和<thinking>两种格式）
        think_pattern = r'<think>(.*?)</think>'
        thinking_pattern = r'<thinking>(.*?)</thinking>'

        think_match = re.search(think_pattern, raw_response, re.DOTALL)
        thinking_match = re.search(thinking_pattern, raw_response, re.DOTALL)

        if think_match:
            thinking_content = think_match.group(1).strip()
            response_content = re.sub(think_pattern, '', raw_response, flags=re.DOTALL).strip()
        elif thinking_match:
            thinking_content = thinking_match.group(1).strip()
            response_content = re.sub(thinking_pattern, '', raw_response, flags=re.DOTALL).strip()
        else:
            thinking_content = ''
            response_content = raw_response

        return {'thinking': thinking_content, 'response': response_content}

    def parse_response(self, response_text: str, prompt_variant: str = "sampled", dataset_source: str = "twitter_sentiment") -> Dict[str, Any]:
        """解析响应文本，提取情感标签，支持单词格式和[Label]: xxx格式"""
        if not response_text:
            return {'raw_answer': '', 'parsed_answer': None}

        raw_answer = response_text.strip()
        parsed_answer = None

        try:
            if prompt_variant == "sampled":
                text = raw_answer
                label = None

                # 优先提取 [Label]: 格式
                if '[Label]:' in text:
                    try:
                        # 提取 [Label]: 之后的内容
                        after_label = text.split('[Label]:', 1)[1].strip()
                        # 取第一行，去除多余内容
                        first_line = after_label.splitlines()[0].strip()

                        # 清理各种格式
                        label = first_line
                        # 移除 <label: 和 > 标签
                        if '<label:' in label:
                            label = label.split('<label:', 1)[1]
                            if '>' in label:
                                label = label.split('>', 1)[0]

                        # 移除其他可能的标记
                        label = label.replace('<', '').replace('>', '').strip()

                        # 如果包含多个词，只取第一个
                        if label.split():
                            label = label.split()[0]

                        logger.debug(f"Extracted label from [Label]: format: '{label}' (from: '{first_line}')")
                    except Exception as e:
                        logger.warning(f"Error extracting from [Label]: format: {e}")
                        label = None

                # 如果没有找到[Label]:格式，回退到正则匹配
                if not label:
                    m = re.search(r"(?i)\b(positive|negative|neutral)\b", text)
                    if m:
                        label = m.group(1).lower()
                        logger.debug(f"Extracted label from regex: '{label}'")

                # 标准化标签格式
                if label:
                    label_lower = label.lower().strip()
                    if label_lower in ['positive', 'pos']:
                        parsed_answer = 'Positive'
                    elif label_lower in ['negative', 'neg']:
                        parsed_answer = 'Negative'
                    elif label_lower in ['neutral', 'neut']:
                        parsed_answer = 'Neutral'
                    else:
                        # 如果不是标准格式，保持原样但首字母大写
                        parsed_answer = label.capitalize()
                        logger.warning(f"Non-standard label format: '{label}' -> '{parsed_answer}'")
                else:
                    parsed_answer = None
                    logger.warning(f"Could not extract sentiment label from: '{raw_answer[:100]}...'")

        except Exception as e:
            logger.error(f"Error parsing response format: {e}")
            parsed_answer = None

        return {'raw_answer': raw_answer, 'parsed_answer': parsed_answer}

    def calculate_total_logprob(self, logprobs_data: Optional[Dict[str, Any]]) -> float:
        """
        计算整个回答的总对数概率（logprob相加）

        Args:
            logprobs_data: VLLM返回的logprobs数据

        Returns:
            float: 总对数概率值，值越高表示模型越确定
        """
        if not logprobs_data:
            return 0.0

        try:
            # 获取每个token的logprobs
            content = logprobs_data.get("content", [])
            if not content:
                return 0.0

            total_logprob = 0.0
            token_count = 0

            for token_data in content:
                # 获取当前token的logprob
                token_logprob = token_data.get("logprob", 0.0)
                total_logprob += token_logprob
                token_count += 1

            logger.debug(f"Total logprob: {total_logprob:.4f} over {token_count} tokens")
            return total_logprob

        except Exception as e:
            logger.error(f"Error calculating total logprob: {e}")
            return 0.0

    def extract_sentiment_token_probabilities(self, logprobs_data: Optional[Dict[str, Any]], raw_response: str = "") -> Dict[str, float]:
        """
        从logprobs数据中提取关键情感词汇的概率
        针对单词输出进行优化

        Args:
            logprobs_data: VLLM返回的logprobs数据
            raw_response: 原始响应文本

        Returns:
            Dict[str, float]: 情感标签到概率的映射 {'positive': 0.x, 'negative': 0.x, 'neutral': 0.x}
        """
        sentiment_probs = {'positive': 0.0, 'negative': 0.0, 'neutral': 0.0}

        if not logprobs_data:
            logger.debug("No logprobs data available, using uniform distribution")
            return {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3}

        try:
            # 获取logprobs内容
            content = logprobs_data.get("content", [])
            if not content:
                logger.debug("No content in logprobs data, using uniform distribution")
                return {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3}

            # 提取实际响应部分（去除thinking内容）
            response_part = raw_response
            if raw_response:
                extracted = self.extract_thinking_content(raw_response)
                response_part = extracted['response'].strip()
                logger.debug(f"Extracted response part: '{response_part}'")

            # 由于现在是单词输出，直接查找情感词token
            found_sentiment_tokens = []

            for i, token_data in enumerate(content):
                token = token_data.get("token", "").strip()
                token_lower = token.lower()
                logprob = token_data.get("logprob", float('-inf'))
                prob = np.exp(logprob)

                # 匹配情感词汇（包括带空格的变体）
                if token_lower in ['positive', 'pos'] or token in [' Positive', ' positive']:
                    sentiment_probs['positive'] = max(sentiment_probs['positive'], prob)
                    found_sentiment_tokens.append(('positive', prob, i, token))
                    logger.debug(f"Found 'positive' token '{token}' at position {i} with prob {prob:.4f}")
                elif token_lower in ['negative', 'neg'] or token in [' Negative', ' negative']:
                    sentiment_probs['negative'] = max(sentiment_probs['negative'], prob)
                    found_sentiment_tokens.append(('negative', prob, i, token))
                    logger.debug(f"Found 'negative' token '{token}' at position {i} with prob {prob:.4f}")
                elif token_lower in ['neutral', 'neut'] or token in [' Neutral', ' neutral']:
                    sentiment_probs['neutral'] = max(sentiment_probs['neutral'], prob)
                    found_sentiment_tokens.append(('neutral', prob, i, token))
                    logger.debug(f"Found 'neutral' token '{token}' at position {i} with prob {prob:.4f}")

            # 记录找到的情感token
            if found_sentiment_tokens:
                logger.debug(f"Found {len(found_sentiment_tokens)} sentiment tokens: {[(t[0], f'{t[1]:.4f}', t[3]) for t in found_sentiment_tokens]}")
            else:
                logger.debug("No sentiment tokens found in logprobs")

            # 如果没有找到任何情感词，使用均匀分布
            total_prob = sum(sentiment_probs.values())
            if total_prob == 0:
                logger.debug("No sentiment tokens found, using uniform distribution")
                sentiment_probs = {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3}
            else:
                # 不需要归一化，因为我们只关心找到的那个词的概率
                # 其他两个词的概率设为0（或很小的值）
                logger.debug(f"Raw sentiment probabilities: {sentiment_probs}")

                # 找到最高概率的情感
                max_sentiment = max(sentiment_probs.keys(), key=lambda k: sentiment_probs[k])
                max_prob = sentiment_probs[max_sentiment]

                # 将找到的情感词概率设为其原始概率，其他设为均匀分布的剩余部分
                remaining_prob = (1.0 - max_prob) / 2 if max_prob < 1.0 else 0.0
                for sentiment in sentiment_probs:
                    if sentiment != max_sentiment:
                        sentiment_probs[sentiment] = remaining_prob

                logger.debug(f"Final sentiment probabilities: {sentiment_probs}")

        except Exception as e:
            logger.error(f"Error extracting sentiment probabilities: {e}")
            sentiment_probs = {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3}

        return sentiment_probs

    def _extract_detailed_logprobs(self, logprobs_data: Optional[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        提取详细的logprobs信息，包括每个token的原始logprob和计算出的概率

        Args:
            logprobs_data: VLLM返回的logprobs数据

        Returns:
            List[Dict]: 每个token的详细信息列表
        """
        if not logprobs_data:
            return []

        detailed_logprobs = []
        content = logprobs_data.get("content", [])

        for i, token_data in enumerate(content):
            token = token_data.get("token", "")
            logprob = token_data.get("logprob", float('-inf'))
            prob = np.exp(logprob) if logprob != float('-inf') else 0.0

            token_info = {
                "position": i,
                "token": token,
                "logprob": logprob,
                "prob": prob
            }

            # 如果有top_logprobs信息，也保存
            if "top_logprobs" in token_data:
                token_info["top_logprobs"] = token_data["top_logprobs"]

            detailed_logprobs.append(token_info)

        return detailed_logprobs

    def get_sentiment_prompts(self, sample_count: Optional[int] = None, seed: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        获取情感分析的prompt模板，遵循原有的访问逻辑

        Args:
            sample_count: 采样数量
            seed: 随机种子

        Returns:
            List[Dict]: prompt字典列表
        """
        task_config = self.config.get('tasks', {}).get('sentiment_analysis', {})
        sample_prompts = sample_count or task_config.get('sample_prompts', 5)

        # 获取所有可用的prompts
        all_prompts = self.prompt_manager.get_task_prompts('sentiment_analysis')

        if not all_prompts:
            logger.warning("No prompts found for sentiment_analysis task")
            return []

        # 如果请求的数量大于等于可用数量，返回所有
        if sample_prompts >= len(all_prompts):
            logger.info(f"Using all {len(all_prompts)} prompts for sentiment_analysis")
            return all_prompts

        # 使用种子随机选择指定数量的prompts
        if seed is not None:
            selected_prompts = self.prompt_manager.get_random_prompts('sentiment_analysis', sample_prompts, seed=seed)
        else:
            selected_prompts = self.prompt_manager.get_random_prompts('sentiment_analysis', sample_prompts)

        if seed is not None:
            logger.info(f"Selected {len(selected_prompts)} prompts from {len(all_prompts)} available for sentiment_analysis with seed {seed}")
        else:
            logger.info(f"Selected {len(selected_prompts)} prompts from {len(all_prompts)} available for sentiment_analysis")

        return selected_prompts

    def analyze_sentiment_with_logprobs(self, input_text: str, prompt_dict: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        分析文本情感并返回logprobs信息，使用原有的prompt管理逻辑

        Args:
            input_text: 要分析的文本
            prompt_dict: prompt字典，如果为None则随机选择一个

        Returns:
            Dict包含：
            - predicted_sentiment: 预测的情感标签
            - confidence: 置信度
            - logprobs: 原始logprobs数据
            - sentiment_probabilities: 各情感标签的概率
            - raw_response: 原始响应文本
        """
        # 选择prompt
        if prompt_dict is None:
            available_prompts = self.get_sentiment_prompts(sample_count=1)
            if not available_prompts:
                raise ValueError("No sentiment analysis prompts available")
            prompt_dict = available_prompts[0]

        # 使用原有的模板变量名
        task_config = self.config.get('tasks', {}).get('sentiment_analysis', {})
        template_variable = task_config.get('template_variable', 'tweet')

        # 构建完整提示词，使用原有的format_prompt方法
        prompt = self.prompt_manager.format_prompt(prompt_dict, **{template_variable: input_text})

        logger.info(f"Analyzing sentiment for text: {input_text[:1000]}...")

        # 调用VLLM
        response = self.call_vllm_with_logprobs(prompt)

        # 提取thinking内容和实际响应
        thinking_response = self.extract_thinking_content(response.content)

        # 解析响应获取情感标签
        parsed_response = self.parse_response(thinking_response['response'], "sampled", "twitter_sentiment")
        predicted_sentiment = parsed_response['parsed_answer']

        # 计算总对数概率
        total_logprob = self.calculate_total_logprob(response.logprobs)

        # 提取情感词汇概率（只在response部分查找）
        sentiment_probabilities = self.extract_sentiment_token_probabilities(response.logprobs, response.content)

        # 计算置信度（使用最高概率作为置信度）
        confidence = max(sentiment_probabilities.values()) if sentiment_probabilities else 0.0

        result = {
            'predicted_sentiment': predicted_sentiment,
            'confidence': confidence,
            'total_logprob': total_logprob,  # 整个回答的总对数概率
            'sentiment_probabilities': sentiment_probabilities,  # 关键情感词的概率
            'raw_response': response.content,
            'thinking_content': thinking_response['thinking'],
            'actual_response': thinking_response['response'],
            'raw_answer': parsed_response['raw_answer'],
            'model': response.model,
            'finish_reason': response.finish_reason,
            'usage': response.usage,
            'prompt_id': prompt_dict.get('id', 'unknown') if prompt_dict else 'random',
            'prompt_raw_text': prompt,  # 添加原始prompt文本
            'logprobs': response.logprobs  # 添加logprobs数据
        }

        logger.info(f"Sentiment analysis result: {predicted_sentiment} (confidence: {confidence:.3f})")

        return result

    def batch_analyze_sentiments(self, texts: List[str], prompt_dict: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        批量分析文本情感

        Args:
            texts: 要分析的文本列表
            prompt_dict: prompt字典，如果为None则为每个文本随机选择

        Returns:
            List[Dict]: 每个文本的分析结果列表
        """
        results = []

        logger.info(f"Starting batch sentiment analysis for {len(texts)} texts")

        # 如果没有指定prompt_dict，获取可用的prompts
        available_prompts = None
        if prompt_dict is None:
            available_prompts = self.get_sentiment_prompts()

        for i, text in enumerate(texts):
            logger.info(f"Processing text {i+1}/{len(texts)}")

            try:
                # 如果没有指定prompt_dict，随机选择一个
                current_prompt_dict = prompt_dict
                if current_prompt_dict is None and available_prompts:
                    current_prompt_dict = random.choice(available_prompts)

                result = self.analyze_sentiment_with_logprobs(text, current_prompt_dict)
                result['text_index'] = i
                result['input_text'] = text
                results.append(result)

                # 添加小延迟避免过快请求
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"Error processing text {i+1}: {e}")
                # 添加错误结果
                error_result = {
                    'text_index': i,
                    'input_text': text,
                    'predicted_sentiment': None,
                    'confidence': 0.0,
                    'logprobs': None,
                    'sentiment_probabilities': {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3},
                    'raw_response': '',
                    'error': str(e)
                }
                results.append(error_result)

        logger.info(f"Batch analysis completed. Processed {len(results)} texts")
        return results

    def process_dataset_from_config(self, sample_count: Optional[int] = None, run_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        处理配置文件中指定的数据集，遵循原有llm_response_generator.py的逻辑：
        1. 对每个数据项使用item_idx作为种子选择sample_prompts个prompt
        2. 每个prompt对每个文本重复attempts_per_prompt次
        3. 保存到MongoDB的collection中

        Args:
            sample_count: 限制处理的样本数量
            run_id: 运行ID，如果为None则生成新的

        Returns:
            List[Dict]: 分析结果列表
        """
        # 加载数据集
        dataset = self.load_dataset_from_config()
        if not dataset:
            logger.error("No dataset loaded")
            return []

        # 限制样本数量
        if sample_count and sample_count < len(dataset):
            dataset = dataset[:sample_count]
            logger.info(f"Limited processing to {sample_count} samples")

        # 获取任务配置
        task_config = self.config.get('tasks', {}).get('sentiment_analysis', {})
        sample_prompts_count = task_config.get('sample_prompts', 5)
        attempts_per_prompt = task_config.get('attempts_per_prompt', 6)
        dataset_source = task_config.get('dataset_source', 'twitter_sentiment')
        task_name = 'sentiment_analysis'

        if not run_id:
            import uuid
            run_id = str(uuid.uuid4())

        logger.info(f"Starting dataset processing with run_id: {run_id}")
        logger.info(f"  - {len(dataset)} records")
        logger.info(f"  - {sample_prompts_count} prompts per record")
        logger.info(f"  - {attempts_per_prompt} attempts per prompt")
        logger.info(f"  - Total: {len(dataset) * sample_prompts_count * attempts_per_prompt} API calls")

        results = []
        total_calls = 0

        # 遵循原有逻辑：外层循环遍历数据项
        for item_idx, record in enumerate(dataset):
            logger.info(f"Processing record {item_idx+1}/{len(dataset)}: ID={record['id']}")

            # 使用item_idx作为种子选择prompts，确保可重现性
            selected_prompts = self.get_sentiment_prompts(sample_prompts_count, seed=item_idx)
            if not selected_prompts:
                logger.error(f"No prompts available for record {item_idx}")
                continue

            # 中层循环：处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号
                logger.info(f"  Using prompt {prompt_number}/{len(selected_prompts)}: {prompt_id}")

                # 内层循环：每个prompt重复attempts_per_prompt次
                for attempt in range(1, attempts_per_prompt + 1):
                    total_calls += 1
                    logger.info(f"    Attempt {attempt}/{attempts_per_prompt} (Call {total_calls})")

                    try:
                        # 分析情感
                        result = self.analyze_sentiment_with_logprobs(record['text'], prompt_dict)

                        # 构建与原有代码一致的MongoDB文档结构
                        document = self._build_mongodb_document(
                            result=result,
                            record=record,
                            item_idx=item_idx,
                            prompt_dict=prompt_dict,
                            prompt_number=prompt_number,
                            attempt=attempt,
                            run_id=run_id,
                            task_name=task_name,
                            dataset_source=dataset_source,
                            task_config=task_config
                        )

                        # 保存到MongoDB（如果配置了）
                        if hasattr(self, 'collection') and self.collection is not None:
                            inserted_id = self.save_to_mongodb(document)
                            if inserted_id:
                                logger.info(f"      Saved to MongoDB with ID: {inserted_id}")
                            else:
                                logger.error(f"      Failed to save to MongoDB")

                        results.append(document)

                        # 添加小延迟
                        time.sleep(0.1)

                    except Exception as e:
                        logger.error(f"Error in call {total_calls}: {e}")
                        # 添加错误结果
                        error_document = self._build_error_document(
                            record=record,
                            item_idx=item_idx,
                            prompt_dict=prompt_dict,
                            prompt_number=prompt_number,
                            attempt=attempt,
                            run_id=run_id,
                            task_name=task_name,
                            dataset_source=dataset_source,
                            task_config=task_config,
                            error=str(e)
                        )
                        results.append(error_document)

        logger.info(f"Dataset processing completed. Made {total_calls} API calls, got {len(results)} results")
        return results

    def save_results_to_json(self, results: List[Dict[str, Any]], output_path: str):
        """
        保存结果到JSON文件

        Args:
            results: 分析结果列表
            output_path: 输出文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"Results saved to: {output_path}")
        except Exception as e:
            logger.error(f"Error saving results: {e}")

    def load_dataset_from_config(self) -> List[Dict[str, Any]]:
        """
        从配置文件指定的数据集加载数据

        Returns:
            List[Dict]: 数据记录列表
        """
        task_config = self.config.get('tasks', {}).get('sentiment_analysis', {})
        data_file = task_config.get('data_file', 'sampled_semeval.csv')
        text_field = task_config.get('text_field', 'text')
        label_field = task_config.get('label_field', 'label')
        id_field = task_config.get('id_field', 'id')
        max_samples = task_config.get('max_samples')

        try:
            import pandas as pd
            df = pd.read_csv(data_file)

            logger.info(f"Loaded {len(df)} records from {data_file}")

            # 限制样本数量
            if max_samples and max_samples < len(df):
                df = df.head(max_samples)
                logger.info(f"Limited to {max_samples} samples")

            # 转换为字典列表
            records = []
            for _, row in df.iterrows():
                record = {
                    'id': row.get(id_field, ''),
                    'text': row.get(text_field, ''),
                    'label': row.get(label_field, ''),
                    'original_data': row.to_dict()
                }
                records.append(record)

            logger.info(f"Processed {len(records)} records for analysis")
            return records

        except Exception as e:
            logger.error(f"Error loading dataset from {data_file}: {e}")
            return []

    def load_texts_from_file(self, file_path: str) -> List[str]:
        """
        从文件加载文本列表

        Args:
            file_path: 文件路径，支持txt、json、csv格式

        Returns:
            List[str]: 文本列表
        """
        file_path_obj = Path(file_path)
        texts = []

        try:
            if file_path_obj.suffix.lower() == '.txt':
                # 每行一个文本
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    texts = [line.strip() for line in f if line.strip()]

            elif file_path_obj.suffix.lower() == '.json':
                # JSON格式，期望是字符串列表或包含text字段的对象列表
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        if data and isinstance(data[0], str):
                            texts = data
                        elif data and isinstance(data[0], dict):
                            texts = [item.get('text', '') for item in data if item.get('text')]

            elif file_path_obj.suffix.lower() == '.csv':
                # CSV格式，假设有text列
                import csv
                with open(file_path_obj, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    texts = [row.get('text', '') for row in reader if row.get('text')]

            logger.info(f"Loaded {len(texts)} texts from {file_path_obj}")
            return texts

        except Exception as e:
            logger.error(f"Error loading texts from {file_path_obj}: {e}")
            return []

    def _init_mongodb(self):
        """初始化MongoDB连接"""
        output_config = self.config.get('output', {})
        if output_config.get('format') == 'mongodb':
            try:
                from pymongo import MongoClient
                mongo_config = output_config.get('mongo', {})
                self.mongo_client = MongoClient(f"mongodb://{mongo_config.get('host', 'localhost')}:{mongo_config.get('port', 27017)}/")
                self.db = self.mongo_client[mongo_config.get('database', 'LLM-UQ')]
                self.collection = self.db[mongo_config.get('collection', 'response_collection')]
                self._create_indexes()
                logger.info("MongoDB connection initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing MongoDB: {e}")
                self.mongo_client = None
                self.db = None
                self.collection = None
        else:
            self.mongo_client = None
            self.db = None
            self.collection = None

    def _create_indexes(self):
        """创建MongoDB索引"""
        if self.collection is None:
            return

        try:
            self.collection.create_index([("run_id", 1)])
            self.collection.create_index([("task_id", 1)])
            self.collection.create_index([("dataset_source", 1)])
            self.collection.create_index([("task_name", 1)])
            self.collection.create_index([("execution_timestamp", -1)])
            self.collection.create_index([("prompt_seed", 1)])
            self.collection.create_index([("prompt_index", 1)])
            logger.info(f"MongoDB indexes created successfully")
        except Exception as e:
            logger.error(f"Error creating MongoDB indexes: {e}")

    def save_to_mongodb(self, data: Dict[str, Any]):
        """保存数据到MongoDB"""
        if self.collection is None:
            logger.error("MongoDB collection not initialized")
            return None

        try:
            result = self.collection.insert_one(data)
            logger.debug(f"Successfully saved document to MongoDB with ID: {result.inserted_id}")
            return result.inserted_id
        except Exception as e:
            logger.error(f"Error saving to MongoDB: {e}")
            return None

    def generate_task_id(self, dataset_source: str, input_id: str, prompt_variant: str) -> str:
        """生成任务ID"""
        task_id = f"task_{dataset_source}_{input_id}_{prompt_variant}"
        logger.debug(f"Generated task ID: {task_id}")
        return task_id

    def _build_mongodb_document(self, result: Dict[str, Any], record: Dict[str, Any],
                               item_idx: int, prompt_dict: Dict[str, Any], prompt_number: int,
                               attempt: int, run_id: str, task_name: str, dataset_source: str,
                               task_config: Dict[str, Any]) -> Dict[str, Any]:
        """构建与原有代码一致的MongoDB文档结构"""
        from datetime import datetime, timezone

        prompt_key = f"{record['id']}_prompt_{prompt_number}"
        task_id = self.generate_task_id(dataset_source, prompt_key, "sampled")

        # 获取模型配置
        model_config = self.config.get('model', {})

        document = {
            "run_id": run_id,
            "task_id": task_id,
            "task_name": task_name,
            "dataset_source": dataset_source,
            "task_category": task_config.get('task_category', 'sentiment_analysis'),
            "input_text": record['text'],
            "reference_answer": record.get('label'),
            "model_identifier": result.get('model', self.model_name),
            "prompt_variant": "sampled",
            "prompt_seed": item_idx,
            "prompt_index": prompt_number,
            "prompt_raw_text": result.get('prompt_raw_text', ''),  # 需要从result中获取
            "generation_config": {
                "temperature": model_config.get('temperature', 0.7),
                "top_p": model_config.get('top_p', 0.95),
                "enable_thinking": model_config.get('enable_thinking', False),
                "enable_logprobs": model_config.get('enable_logprobs', True),
                "top_logprobs": model_config.get('top_logprobs', 0)
            },
            "task_attempt_prompt": attempt,
            "task_attempt_total": None,
            "raw_response": result.get('raw_response', ''),
            "thinking_content": result.get('thinking_content', ''),
            "actual_response": result.get('actual_response', ''),
            "response_logprobs": result.get('logprobs'),
            "finish_reason": result.get('finish_reason'),
            "raw_answer": result.get('raw_answer', ''),
            "parsed_answer": result.get('predicted_sentiment'),
            "parsed_reason": None,  # VLLM版本暂不支持reasoning
            "execution_timestamp": datetime.now(timezone.utc),
            # VLLM特有字段
            "total_logprob": result.get('total_logprob', 0.0),
            "sentiment_probabilities": result.get('sentiment_probabilities', {}),
            "confidence": result.get('confidence', 0.0),
            # 详细的logprobs信息
            "detailed_logprobs": self._extract_detailed_logprobs(result.get('logprobs'))
        }

        return document

    def _build_error_document(self, record: Dict[str, Any], item_idx: int,
                             prompt_dict: Dict[str, Any], prompt_number: int, attempt: int,
                             run_id: str, task_name: str, dataset_source: str,
                             task_config: Dict[str, Any], error: str) -> Dict[str, Any]:
        """构建错误情况下的MongoDB文档结构"""
        from datetime import datetime, timezone

        prompt_key = f"{record['id']}_prompt_{prompt_number}"
        task_id = self.generate_task_id(dataset_source, prompt_key, "sampled")

        # 获取模型配置
        model_config = self.config.get('model', {})

        document = {
            "run_id": run_id,
            "task_id": task_id,
            "task_name": task_name,
            "dataset_source": dataset_source,
            "task_category": task_config.get('task_category', 'sentiment_analysis'),
            "input_text": record['text'],
            "reference_answer": record.get('label'),
            "model_identifier": self.model_name,
            "prompt_variant": "sampled",
            "prompt_seed": item_idx,
            "prompt_index": prompt_number,
            "prompt_raw_text": '',
            "generation_config": {
                "temperature": model_config.get('temperature', 0.7),
                "top_p": model_config.get('top_p', 0.95),
                "enable_thinking": model_config.get('enable_thinking', True),
                "enable_logprobs": model_config.get('enable_logprobs', True),
                "top_logprobs": model_config.get('top_logprobs', 0)
            },
            "task_attempt_prompt": attempt,
            "task_attempt_total": None,
            "raw_response": '',
            "thinking_content": '',
            "actual_response": '',
            "response_logprobs": None,
            "finish_reason": None,
            "raw_answer": '',
            "parsed_answer": None,
            "parsed_reason": None,
            "execution_timestamp": datetime.now(timezone.utc),
            # 错误信息
            "error": error,
            # VLLM特有字段（默认值）
            "total_logprob": 0.0,
            "sentiment_probabilities": {'positive': 1/3, 'negative': 1/3, 'neutral': 1/3},
            "confidence": 0.0,
            "detailed_logprobs": []
        }

        return document

    def process_semeval_data_like_original(self, data: List[Dict[str, Any]], run_id: Optional[str] = None) -> str:
        """
        完全按照原有llm_response_generator.py的process_semeval_data逻辑处理数据

        Args:
            data: 数据列表
            run_id: 运行ID，如果为None则生成新的

        Returns:
            str: 运行ID
        """
        task_name = "sentiment_analysis"
        task_config = self.config.get('tasks', {}).get(task_name, {})

        if not run_id:
            import uuid
            run_id = str(uuid.uuid4())

        task_attempt_total = 0
        dataset_source = task_config.get('dataset_source', 'twitter_sentiment')
        attempts_per_prompt = task_config.get('attempts_per_prompt', 6)
        sample_prompts = task_config.get('sample_prompts', 5)

        logger.info(f"开始处理SemEval数据，共 {len(data)} 个项目")
        logger.info(f"每个项目随机选择{sample_prompts}个prompt，每个prompt尝试 {attempts_per_prompt} 次")
        logger.info(f"运行ID: {run_id}")

        # 统计跳过和需要处理的项目
        skipped_items = 0
        to_process_items = 0

        for item_idx, item in enumerate(data):
            # 从配置中获取字段名
            id_field = task_config.get('id_field', 'id')
            text_field = task_config.get('text_field', 'text')
            label_field = task_config.get('label_field', 'label')

            input_text = item[text_field]
            reference_answer = item.get(label_field) if label_field else None
            item_id = item[id_field]

            logger.info(f"处理SemEval项目 {item_idx + 1}/{len(data)}: {item_id}")

            # 获取当前任务的prompt模板，使用数据项索引作为随机种子确保可重现性
            selected_prompts = self.get_sentiment_prompts(sample_prompts, seed=item_idx)

            # 处理每个选中的prompt
            for prompt_idx, prompt_dict in enumerate(selected_prompts):
                prompt_id = prompt_dict.get('id', f'prompt_{prompt_idx+1}')
                prompt_number = prompt_idx + 1  # 转换为1-N的编号

                to_process_items += 1
                logger.info(f"处理SemEval项目 {item_id} 使用 {prompt_id}...")

                # 处理每次尝试
                for current_attempt in range(1, attempts_per_prompt + 1):
                    task_id_local = self.generate_task_id(dataset_source, f"{item_id}_prompt_{prompt_number}", "sampled")
                    template_var_local = task_config.get('template_variable', 'tweet')

                    # 使用JSON prompt管理器格式化prompt
                    final_prompt_local = self.prompt_manager.format_prompt(
                        prompt_dict, **{template_var_local: input_text}
                    )

                    logger.info(f"  尝试 {current_attempt}/{attempts_per_prompt}...")

                    try:
                        # 调用VLLM
                        vllm_response = self.call_vllm_with_logprobs(final_prompt_local)

                        if not vllm_response.content:
                            logger.warning(f"    第 {current_attempt} 次尝试收到空响应")
                            continue

                        # 提取thinking内容
                        thinking_response_local = self.extract_thinking_content(vllm_response.content)

                        # 解析响应
                        parsed_response_local = self.parse_response(thinking_response_local['response'], "sampled", dataset_source)

                        # 获取模型配置
                        model_config_local = self.config.get('model', {})

                        # 构建与原有代码完全一致的文档结构
                        document_local = {
                            "run_id": run_id,
                            "task_id": task_id_local,
                            "task_name": task_name,
                            "dataset_source": dataset_source,
                            "task_category": task_config.get('task_category', 'sentiment_analysis'),
                            "input_text": input_text,
                            "reference_answer": reference_answer,
                            "model_identifier": vllm_response.model,
                            "prompt_variant": "sampled",
                            "prompt_seed": item_idx,
                            "prompt_index": prompt_number,
                            "prompt_raw_text": final_prompt_local,
                            "generation_config": {
                                "temperature": model_config_local.get('temperature', 0.7),
                                "top_p": model_config_local.get('top_p', 0.95),
                                "enable_thinking": model_config_local.get('enable_thinking', True),
                                "enable_logprobs": model_config_local.get('enable_logprobs', True),
                                "top_logprobs": model_config_local.get('top_logprobs', 0)
                            },
                            "task_attempt_prompt": current_attempt,
                            "task_attempt_total": None,
                            "raw_response": vllm_response.content,
                            "thinking_content": thinking_response_local['thinking'],
                            "actual_response": thinking_response_local['response'],
                            "response_logprobs": vllm_response.logprobs,
                            "finish_reason": vllm_response.finish_reason,
                            "raw_answer": parsed_response_local['raw_answer'],
                            "parsed_answer": parsed_response_local['parsed_answer'],
                            "parsed_reason": None,  # VLLM版本暂不支持reasoning
                            "execution_timestamp": self._get_utc_timestamp(),
                            # VLLM特有字段
                            "total_logprob": self.calculate_total_logprob(vllm_response.logprobs),
                            "sentiment_probabilities": self.extract_sentiment_token_probabilities(vllm_response.logprobs, vllm_response.content),
                            "confidence": max(self.extract_sentiment_token_probabilities(vllm_response.logprobs, vllm_response.content).values()) if vllm_response.logprobs else 0.0
                        }

                        # 保存到MongoDB
                        if hasattr(self, 'collection') and self.collection is not None:
                            inserted_id = self.save_to_mongodb(document_local)
                            if inserted_id:
                                logger.info(f"    保存到MongoDB，ID: {inserted_id}")
                                task_attempt_total += 1
                            else:
                                logger.error(f"    保存到MongoDB失败")
                        else:
                            logger.warning("MongoDB未配置，跳过保存")

                    except Exception as e:
                        logger.error(f"处理第 {current_attempt} 次尝试时出错: {e}")
                        continue

        logger.info(f"SemEval数据处理完成。跳过 {skipped_items} 个项目，处理 {to_process_items} 个项目，总计 {task_attempt_total} 次尝试")
        return run_id

    def _get_utc_timestamp(self):
        """获取UTC时间戳"""
        from datetime import datetime, timezone
        return datetime.now(timezone.utc)

    def check_existing_progress(self, run_id: Optional[str] = None) -> Dict[str, Any]:
        """检查现有进度，返回已处理的项目信息"""
        logger.info("检查现有进度...")

        if self.collection is None:
            logger.warning("MongoDB未配置，无法检查进度")
            return {"run_id": None, "processed_items": {}, "total_attempts": 0}

        # 如果没有指定run_id，查找最近的run_id
        if not run_id:
            recent_run = self.collection.find_one(
                {},
                sort=[("execution_timestamp", -1)]
            )
            if recent_run:
                run_id = recent_run.get("run_id")
                logger.info(f"使用最近的run_id: {run_id}")
            else:
                logger.info("没有找到现有的运行记录")
                return {"run_id": None, "processed_items": {}, "total_attempts": 0}

        # 统计已处理的项目
        pipeline = [
            {"$match": {"run_id": run_id}},
            {"$group": {
                "_id": {
                    "dataset_source": "$dataset_source",
                    "task_id": "$task_id",
                    "prompt_variant": "$prompt_variant"
                },
                "attempts": {"$sum": 1},
                "last_attempt": {"$max": "$task_attempt_prompt"}
            }}
        ]

        processed_items = {}
        total_attempts = 0

        for result in self.collection.aggregate(pipeline):
            key = f"{result['_id']['dataset_source']}_{result['_id']['task_id']}_{result['_id']['prompt_variant']}"
            processed_items[key] = {
                "dataset_source": result['_id']['dataset_source'],
                "task_id": result['_id']['task_id'],
                "prompt_variant": result['_id']['prompt_variant'],
                "attempts": result['attempts'],
                "last_attempt": result['last_attempt']
            }
            total_attempts += result['attempts']

        logger.info(f"找到 {len(processed_items)} 个已处理的项目，总计 {total_attempts} 次尝试")
        return {
            "run_id": run_id,
            "processed_items": processed_items,
            "total_attempts": total_attempts
        }

    def should_skip_item(self, dataset_source: str, item_id: str, prompt_variant: str,
                        processed_items: Dict[str, Any], attempts_per_variant: int) -> bool:
        """检查是否应该跳过某个项目"""
        key = f"{dataset_source}_{item_id}_{prompt_variant}"

        if key in processed_items:
            item_info = processed_items[key]
            if item_info['attempts'] >= attempts_per_variant:
                logger.debug(f"跳过 {key} - 已完成 {item_info['attempts']}/{attempts_per_variant} 次尝试")
                return True
            else:
                logger.info(f"继续 {key} - 已完成 {item_info['attempts']}/{attempts_per_variant} 次尝试")
                return False

        return False

    def get_next_attempt_number(self, dataset_source: str, item_id: str, prompt_variant: str,
                               processed_items: Dict[str, Any]) -> int:
        """获取下一个尝试编号"""
        key = f"{dataset_source}_{item_id}_{prompt_variant}"

        if key in processed_items:
            return processed_items[key]['attempts'] + 1
        else:
            return 1


def main():
    """主函数，提供命令行接口"""
    import argparse

    parser = argparse.ArgumentParser(description='VLLM Sentiment Analysis Generator')
    parser.add_argument('--input', '-i', type=str, help='Input file path (txt/json/csv) or single text')
    parser.add_argument('--output', '-o', type=str, default='sentiment_results.json',
                       help='Output JSON file path')
    parser.add_argument('--vllm-host', type=str, default='http://localhost:8000',
                       help='VLLM server host')
    parser.add_argument('--api-key', type=str, default='token-abc123',
                       help='VLLM API key')
    parser.add_argument('--model', type=str, default='LLM-Research/Meta-Llama-3.1-8B-Instruct',
                       help='Model name')
    parser.add_argument('--config', type=str, help='Configuration file path')
    parser.add_argument('--prompt-id', type=str,
                       help='Specific prompt ID to use (e.g., sentiment_01)')
    parser.add_argument('--single-text', type=str, help='Analyze a single text directly')
    parser.add_argument('--dataset', action='store_true', help='Process dataset from config file')
    parser.add_argument('--dataset-original', action='store_true', help='Process dataset using original llm_response_generator logic')
    parser.add_argument('--sample-count', type=int, help='Limit number of samples to process')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    parser.add_argument('--seed', type=int, help='Random seed for prompt selection')
    parser.add_argument('--run-id', type=str, help='Specify run ID for MongoDB storage')

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # 初始化生成器
        generator = VLLMSentimentGenerator(
            vllm_host=args.vllm_host,
            api_key=args.api_key,
            model_name=args.model,
            config_path=args.config
        )

        # 准备prompt_dict
        prompt_dict = None
        if args.prompt_id:
            prompt_dict = generator.prompt_manager.get_prompt_by_id('sentiment_analysis', args.prompt_id)
            if not prompt_dict:
                logger.error(f"Prompt ID '{args.prompt_id}' not found")
                return 1

        if args.single_text:
            # 分析单个文本
            logger.info("Analyzing single text...")
            result = generator.analyze_sentiment_with_logprobs(args.single_text, prompt_dict)

            print("\n=== Sentiment Analysis Result ===")
            print(f"Text: {args.single_text}")
            print(f"Predicted Sentiment: {result['predicted_sentiment']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print(f"Sentiment Probabilities: {result['sentiment_probabilities']}")
            print(f"Raw Response: {result['raw_response']}")
            if prompt_dict:
                print(f"Used Prompt ID: {prompt_dict.get('id', 'unknown')}")

            # 保存结果
            generator.save_results_to_json([result], args.output)

        elif args.dataset:
            # 处理配置文件中的数据集
            logger.info("Processing dataset from config file...")
            results = generator.process_dataset_from_config(args.sample_count, args.run_id)

            if results:
                generator.save_results_to_json(results, args.output)

                # 打印统计信息
                successful = len([r for r in results if r.get('predicted_sentiment')])
                total_records = len(results)

                print(f"\n=== Dataset Analysis Summary ===")
                print(f"Total records: {total_records}")
                print(f"Successfully processed: {successful}")
                print(f"Failed: {total_records - successful}")
                print(f"Results saved to: {args.output}")

                # 打印准确率统计（如果有真实标签）
                correct_predictions = 0
                total_with_labels = 0

                for result in results:
                    if result.get('true_label') and result.get('predicted_sentiment'):
                        total_with_labels += 1
                        true_label = result['true_label'].lower()
                        pred_label = result['predicted_sentiment'].lower()
                        if true_label == pred_label:
                            correct_predictions += 1

                if total_with_labels > 0:
                    accuracy = correct_predictions / total_with_labels
                    print(f"Accuracy: {accuracy:.3f} ({correct_predictions}/{total_with_labels})")

                if prompt_dict:
                    print(f"Used Prompt ID: {prompt_dict.get('id', 'unknown')}")
            else:
                logger.error("No results from dataset processing")

        elif args.dataset_original:
            # 使用与原有llm_response_generator.py完全一致的逻辑处理数据集
            logger.info("Processing dataset using original llm_response_generator logic...")

            # 加载数据集
            dataset = generator.load_dataset_from_config()
            if not dataset:
                logger.error("No dataset loaded")
                return 1

            # 限制样本数量
            if args.sample_count and args.sample_count < len(dataset):
                dataset = dataset[:args.sample_count]
                logger.info(f"Limited processing to {args.sample_count} samples")

            # 处理数据集
            run_id = generator.process_semeval_data_like_original(dataset, args.run_id)

            print(f"\n=== Original Logic Processing Complete ===")
            print(f"Run ID: {run_id}")
            print(f"Processed {len(dataset)} records")
            print(f"Data saved to MongoDB collection")
            if args.run_id:
                print(f"Used specified run ID: {args.run_id}")
            else:
                print(f"Generated new run ID: {run_id}")

        elif args.dataset_original:
            # 使用与原有llm_response_generator.py完全一致的逻辑处理数据集
            logger.info("Processing dataset using original llm_response_generator logic...")

            # 加载数据集
            dataset = generator.load_dataset_from_config()
            if not dataset:
                logger.error("No dataset loaded")
                return 1

            # 限制样本数量
            if args.sample_count and args.sample_count < len(dataset):
                dataset = dataset[:args.sample_count]
                logger.info(f"Limited processing to {args.sample_count} samples")

            # 处理数据集
            run_id = generator.process_semeval_data_like_original(dataset, args.run_id)

            print(f"\n=== Original Logic Processing Complete ===")
            print(f"Run ID: {run_id}")
            print(f"Processed {len(dataset)} records")
            print(f"Data saved to MongoDB collection")
            if args.run_id:
                print(f"Used specified run ID: {args.run_id}")
            else:
                print(f"Generated new run ID: {run_id}")

        elif args.input:
            # 批量处理文件
            if Path(args.input).exists():
                logger.info(f"Loading texts from file: {args.input}")
                texts = generator.load_texts_from_file(args.input)

                if texts:
                    results = generator.batch_analyze_sentiments(texts, prompt_dict)
                    generator.save_results_to_json(results, args.output)

                    # 打印统计信息
                    successful = len([r for r in results if r.get('predicted_sentiment')])
                    print(f"\n=== Batch Analysis Summary ===")
                    print(f"Total texts: {len(texts)}")
                    print(f"Successfully processed: {successful}")
                    print(f"Failed: {len(texts) - successful}")
                    print(f"Results saved to: {args.output}")
                    if prompt_dict:
                        print(f"Used Prompt ID: {prompt_dict.get('id', 'unknown')}")
                else:
                    logger.error("No texts loaded from input file")
            else:
                # 将输入作为单个文本处理
                logger.info("Treating input as single text...")
                result = generator.analyze_sentiment_with_logprobs(args.input, prompt_dict)
                generator.save_results_to_json([result], args.output)
        else:
            # 交互模式
            print("VLLM Sentiment Analysis Generator")
            print("Enter texts to analyze (empty line to quit):")
            if prompt_dict:
                print(f"Using Prompt ID: {prompt_dict.get('id', 'unknown')}")

            while True:
                text = input("\nText: ").strip()
                if not text:
                    break

                result = generator.analyze_sentiment_with_logprobs(text, prompt_dict)
                print(f"Sentiment: {result['predicted_sentiment']} (confidence: {result['confidence']:.3f})")
                print(f"Probabilities: {result['sentiment_probabilities']}")
                if not prompt_dict:
                    print(f"Used Prompt ID: {result.get('prompt_id', 'random')}")

    except Exception as e:
        logger.error(f"Error in main: {e}")
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
