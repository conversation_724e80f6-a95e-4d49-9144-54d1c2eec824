#!/usr/bin/env python3
"""
测试情感分析Token Entropy计算
"""

import os
import sys
import json
import logging
from typing import Dict, Any, List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(__file__))

from run_sentiment_token_entropy import SentimentTokenEntropyCalculator
from uq_methods.implementations.token_entropy import TokenEntropyUQ, MeanTokenEntropyUQ

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_logprobs_data() -> List[Dict[str, Any]]:
    """创建测试用的logprobs数据，模拟VLLM格式"""
    
    # 模拟情感分析的响应数据
    test_responses = [
        {
            "parsed_answer": "Positive",
            "raw_response": "Positive",
            "response_logprobs": {
                "content": [
                    {
                        "token": "Positive",
                        "logprob": -0.0001,  # 高概率
                        "bytes": [80, 111, 115, 105, 116, 105, 118, 101],
                        "top_logprobs": [
                            {"token": "Positive", "logprob": -0.0001},
                            {"token": "Negative", "logprob": -8.5},
                            {"token": "Neutral", "logprob": -9.2}
                        ]
                    },
                    {
                        "token": "<|im_end|>",
                        "logprob": -0.004,
                        "bytes": [60, 124, 105, 109, 95, 101, 110, 100, 124, 62],
                        "top_logprobs": []
                    }
                ]
            },
            "detailed_logprobs": [
                {
                    "position": 0,
                    "token": "Positive",
                    "logprob": -0.0001,
                    "prob": 0.9999,
                    "top_logprobs": []
                }
            ]
        },
        {
            "parsed_answer": "Positive",
            "raw_response": "Positive",
            "response_logprobs": {
                "content": [
                    {
                        "token": "Positive",
                        "logprob": -0.0002,  # 稍低概率
                        "bytes": [80, 111, 115, 105, 116, 105, 118, 101],
                        "top_logprobs": [
                            {"token": "Positive", "logprob": -0.0002},
                            {"token": "Negative", "logprob": -7.8},
                            {"token": "Neutral", "logprob": -8.9}
                        ]
                    },
                    {
                        "token": "<|im_end|>",
                        "logprob": -0.003,
                        "bytes": [60, 124, 105, 109, 95, 101, 110, 100, 124, 62],
                        "top_logprobs": []
                    }
                ]
            },
            "detailed_logprobs": [
                {
                    "position": 0,
                    "token": "Positive",
                    "logprob": -0.0002,
                    "prob": 0.9998,
                    "top_logprobs": []
                }
            ]
        },
        {
            "parsed_answer": "Negative",
            "raw_response": "Negative",
            "response_logprobs": {
                "content": [
                    {
                        "token": "Negative",
                        "logprob": -0.1,  # 中等概率
                        "bytes": [78, 101, 103, 97, 116, 105, 118, 101],
                        "top_logprobs": [
                            {"token": "Negative", "logprob": -0.1},
                            {"token": "Positive", "logprob": -2.3},
                            {"token": "Neutral", "logprob": -3.1}
                        ]
                    },
                    {
                        "token": "<|im_end|>",
                        "logprob": -0.005,
                        "bytes": [60, 124, 105, 109, 95, 101, 110, 100, 124, 62],
                        "top_logprobs": []
                    }
                ]
            },
            "detailed_logprobs": [
                {
                    "position": 0,
                    "token": "Negative",
                    "logprob": -0.1,
                    "prob": 0.9048,
                    "top_logprobs": []
                }
            ]
        }
    ]
    
    return test_responses


def test_token_entropy_methods():
    """测试Token Entropy方法"""
    logger.info("Testing Token Entropy methods...")
    
    # 创建测试数据
    test_responses = create_test_logprobs_data()
    
    # 提取响应文本和logprobs
    response_texts = [resp["parsed_answer"] for resp in test_responses]
    responses_with_probs = [
        {
            'logprobs': resp['response_logprobs'],
            'raw_response': resp['raw_response'],
            'detailed_logprobs': resp['detailed_logprobs']
        }
        for resp in test_responses
    ]
    
    logger.info(f"Test responses: {response_texts}")
    
    # 测试TokenEntropyUQ
    logger.info("\n=== Testing TokenEntropyUQ ===")
    token_entropy_uq = TokenEntropyUQ(verbose=True)
    token_result = token_entropy_uq.compute_uncertainty(response_texts, responses_with_probs)
    
    print(f"TokenEntropy Result:")
    print(f"  Uncertainty Score: {token_result.get('uncertainty_score', 'N/A')}")
    print(f"  Total Tokens: {token_result.get('total_tokens', 'N/A')}")
    print(f"  Avg Tokens per Response: {token_result.get('avg_tokens_per_response', 'N/A')}")
    print(f"  Method: {token_result.get('method', 'N/A')}")
    
    if 'entropy_statistics' in token_result.get('metadata', {}):
        stats = token_result['metadata']['entropy_statistics']
        print(f"  Entropy Stats - Mean: {stats.get('mean', 'N/A'):.4f}, "
              f"Std: {stats.get('std', 'N/A'):.4f}, "
              f"Min: {stats.get('min', 'N/A'):.4f}, "
              f"Max: {stats.get('max', 'N/A'):.4f}")
    
    # 测试MeanTokenEntropyUQ
    logger.info("\n=== Testing MeanTokenEntropyUQ ===")
    mean_token_entropy_uq = MeanTokenEntropyUQ(verbose=True)
    mean_result = mean_token_entropy_uq.compute_uncertainty(response_texts, responses_with_probs)
    
    print(f"MeanTokenEntropy Result:")
    print(f"  Uncertainty Score: {mean_result.get('uncertainty_score', 'N/A')}")
    print(f"  Mean Token Entropy: {mean_result.get('mean_token_entropy', 'N/A')}")
    print(f"  Num Valid Responses: {mean_result.get('num_valid_responses', 'N/A')}")
    print(f"  Method: {mean_result.get('method', 'N/A')}")
    
    if 'prob_statistics' in mean_result:
        stats = mean_result['prob_statistics']
        print(f"  Prob Stats - Mean: {stats.get('mean', 'N/A'):.4f}, "
              f"Std: {stats.get('std', 'N/A'):.4f}, "
              f"Min: {stats.get('min', 'N/A'):.4f}, "
              f"Max: {stats.get('max', 'N/A'):.4f}")
    
    return token_result, mean_result


def test_calculator_with_mock_data():
    """测试计算器类（使用模拟数据）"""
    logger.info("\n=== Testing SentimentTokenEntropyCalculator (Mock Data) ===")
    
    # 创建模拟的响应组数据
    mock_group = {
        'responses': create_test_logprobs_data(),
        'meta': {
            'input_text': 'I love this beautiful sunny day! 😊',
            'prompt_seed': 'sentiment_01',
            'task_name': 'sentiment_analysis',
            'dataset_source': 'twitter_sentiment',
            'model_identifier': 'test_model',
            'reference_answer': 'Positive'
        }
    }
    
    # 创建计算器（不连接真实数据库）
    calculator = SentimentTokenEntropyCalculator(verbose=True)
    
    # 测试单个组的计算
    result = calculator.compute_token_entropy_for_group(mock_group)
    
    if result:
        print(f"Group Calculation Result:")
        print(f"  Input Text: {result['meta_info']['input_text']}")
        print(f"  Num Responses: {result['meta_info']['num_responses']}")
        print(f"  Token Entropy Score: {result['token_entropy'].get('uncertainty_score', 'N/A')}")
        print(f"  Mean Token Entropy Score: {result['mean_token_entropy'].get('uncertainty_score', 'N/A')}")
        print(f"  Response Distribution: {result['responses_summary']['response_distribution']}")
        print(f"  Unique Responses: {result['responses_summary']['unique_responses']}")
    else:
        print("Failed to compute token entropy for group")
    
    return result


def main():
    """运行所有测试"""
    logger.info("Starting Token Entropy Tests...")
    
    try:
        # 测试UQ方法
        token_result, mean_result = test_token_entropy_methods()
        
        # 测试计算器
        group_result = test_calculator_with_mock_data()
        
        # 保存测试结果
        test_results = {
            "token_entropy_result": token_result,
            "mean_token_entropy_result": mean_result,
            "group_calculation_result": group_result,
            "test_timestamp": "2025-01-27"
        }
        
        with open("test_token_entropy_results.json", "w", encoding="utf-8") as f:
            json.dump(test_results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info("Test results saved to test_token_entropy_results.json")
        logger.info("All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    main()
