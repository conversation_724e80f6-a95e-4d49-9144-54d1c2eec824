#!/usr/bin/env python3
"""
运行完整的情感分析任务
处理data/sampled_semeval.csv中的450条数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vllm_sentment_generator import VLLMSentimentGenerator
import logging
import uuid
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    """运行完整的情感分析任务"""
    
    print("="*60)
    print("VLLM Sentiment Analysis - Processing 450 Records")
    print("="*60)
    
    # 初始化生成器
    try:
        generator = VLLMSentimentGenerator(
            vllm_host="http://localhost:8000",
            api_key="token-abc123",
            model_name="Qwen/Qwen3-14B-FP8"
        )
        logger.info("VLLM Sentiment Generator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize generator: {e}")
        return 1
    
    # 验证数据集
    dataset = generator.load_dataset_from_config()
    if not dataset:
        logger.error("Failed to load dataset")
        return 1
    
    print(f"✅ Dataset loaded: {len(dataset)} records")
    print(f"📄 Data file: data/sampled_semeval.csv")
    
    # 显示配置信息
    task_config = generator.config.get('tasks', {}).get('sentiment_analysis', {})
    sample_prompts = task_config.get('sample_prompts', 5)
    attempts_per_prompt = task_config.get('attempts_per_prompt', 6)
    
    total_api_calls = len(dataset) * sample_prompts * attempts_per_prompt
    
    print(f"\n📊 Processing Configuration:")
    print(f"   - Records to process: {len(dataset)}")
    print(f"   - Prompts per record: {sample_prompts}")
    print(f"   - Attempts per prompt: {attempts_per_prompt}")
    print(f"   - Total API calls: {total_api_calls}")
    print(f"   - Thinking mode: {'❌ Disabled' if not generator.config.get('model', {}).get('enable_thinking', True) else '✅ Enabled'}")
    print(f"   - Single word output: ✅ Enabled")
    print(f"   - Detailed logprobs: ✅ Enabled")
    
    # 生成运行ID
    run_id = str(uuid.uuid4())
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"\n🚀 Starting processing...")
    print(f"   - Run ID: {run_id}")
    print(f"   - Timestamp: {timestamp}")
    
    # 询问用户确认
    response = input(f"\nProceed with processing {total_api_calls} API calls? (y/N): ")
    if response.lower() != 'y':
        print("❌ Processing cancelled by user")
        return 0
    
    try:
        # 开始处理
        print(f"\n⏳ Processing started at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 使用原始逻辑处理数据集
        final_run_id = generator.process_semeval_data_like_original(dataset, run_id)
        
        print(f"\n✅ Processing completed!")
        print(f"   - Final Run ID: {final_run_id}")
        print(f"   - Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 显示MongoDB信息
        if hasattr(generator, 'collection') and generator.collection is not None:
            try:
                count = generator.collection.count_documents({"run_id": final_run_id})
                print(f"   - Records saved to MongoDB: {count}")
                print(f"   - Database: {generator.db.name}")
                print(f"   - Collection: {generator.collection.name}")
            except Exception as e:
                logger.warning(f"Could not count MongoDB documents: {e}")
        else:
            print("   - MongoDB not configured")
        
        return 0
        
    except KeyboardInterrupt:
        print(f"\n⚠️  Processing interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Error during processing: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
