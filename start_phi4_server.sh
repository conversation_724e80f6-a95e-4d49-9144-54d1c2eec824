#!/bin/bash
# 启动Phi-4模型作为OpenAI兼容的API服务器

# 设置使用GPU 1
export CUDA_VISIBLE_DEVICES=1
echo "🎮 Set CUDA_VISIBLE_DEVICES=1"

# 默认参数
MODEL_PATH=""
PORT=8000
HOST="0.0.0.0"
GPU_LAYERS=32
CTX_SIZE=4096
THREADS=8

# 查找模型文件
find_model() {
    local paths=(
        "./phi-4-Q4_K_M.gguf"
        "./models/phi-4-Q4_K_M.gguf"
        "$HOME/models/phi-4-Q4_K_M.gguf"
        "./phi4-Q4_K_M.gguf"
        "./models/phi4-Q4_K_M.gguf"
        "/models/phi-4-Q4_K_M.gguf"
        "/data/models/phi-4-Q4_K_M.gguf"
    )
    
    for path in "${paths[@]}"; do
        if [ -f "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    return 1
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--model)
            MODEL_PATH="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --gpu-layers)
            GPU_LAYERS="$2"
            shift 2
            ;;
        --ctx-size)
            CTX_SIZE="$2"
            shift 2
            ;;
        --threads)
            THREADS="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -m, --model PATH     Path to Phi-4 GGUF model file"
            echo "  -p, --port PORT      Port to listen on (default: 8000)"
            echo "  --host HOST          Host to bind to (default: 0.0.0.0)"
            echo "  --gpu-layers N       Number of GPU layers (default: 32)"
            echo "  --ctx-size N         Context size (default: 4096)"
            echo "  --threads N          Number of threads (default: 8)"
            echo "  -h, --help           Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# 查找模型文件
if [ -z "$MODEL_PATH" ]; then
    echo "🔍 Searching for Phi-4 model..."
    MODEL_PATH=$(find_model)
    if [ $? -ne 0 ]; then
        echo "❌ Phi-4 model not found!"
        echo "Please specify the model path with -m or place the model in:"
        echo "  - Current directory"
        echo "  - ./models/"
        echo "  - ~/models/"
        exit 1
    fi
fi

# 检查模型文件是否存在
if [ ! -f "$MODEL_PATH" ]; then
    echo "❌ Model file not found: $MODEL_PATH"
    exit 1
fi

echo "📁 Using model: $MODEL_PATH"
echo "🌐 Starting server on $HOST:$PORT"
echo "🎮 GPU layers: $GPU_LAYERS"
echo "📏 Context size: $CTX_SIZE"
echo "🧵 Threads: $THREADS"

# 检查llama-cpp-python是否安装
if ! python3 -c "import llama_cpp.server" 2>/dev/null; then
    echo "❌ llama-cpp-python not found!"
    echo "Installing llama-cpp-python with CUDA support..."
    CMAKE_ARGS="-DLLAMA_CUBLAS=on" pip3 install llama-cpp-python --force-reinstall --no-cache-dir
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install llama-cpp-python"
        exit 1
    fi
fi

echo "🚀 Starting Phi-4 server..."
echo "=" * 60
echo "Server will be available at:"
echo "  http://$HOST:$PORT"
echo "  OpenAI endpoint: http://$HOST:$PORT/v1/chat/completions"
echo "=" * 60
echo "Press Ctrl+C to stop the server"
echo ""

# 启动服务器
python3 -m llama_cpp.server \
    --model "$MODEL_PATH" \
    --host "$HOST" \
    --port "$PORT" \
    --n_gpu_layers "$GPU_LAYERS" \
    --n_ctx "$CTX_SIZE" \
    --n_threads "$THREADS" \
    --chat_format chatml
