{"metadata": {"version": "0.1", "description": "Unified prompt configuration for LLM uncertainty analysis system", "last_updated": "2025-08-13"}, "tasks": {"sentiment_analysis": {"task_name": "sentiment_analysis", "description": "Twitter sentiment classification task", "output_format": "Single word: Positive, Negative, or Neutral", "template_variables": ["tweet"], "expected_labels": ["Positive", "Negative", "Neutral"], "prompts": [{"id": "sentiment_01", "template": "Classify the sentiment of the following tweet. Respond with only one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "analytical", "reasoning": false}, {"id": "sentiment_02", "template": "Determine the sentiment polarity of the tweet below. Answer with exactly one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "formal", "reasoning": false}, {"id": "sentiment_03", "template": "What is the emotional tone of this tweet? Reply with a single word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "conversational", "reasoning": false}, {"id": "sentiment_04", "template": "Classify the sentiment expressed in the following tweet. Output only one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "instructional", "reasoning": false}, {"id": "sentiment_05", "template": "Evaluate the sentiment of this tweet. Respond with just one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "evaluative", "reasoning": false}, {"id": "sentiment_06", "template": "Read the tweet below and determine its sentiment. Answer with one word only: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "direct", "reasoning": false}, {"id": "sentiment_07", "template": "Assess the emotional content of this tweet. Classify with a single word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "emotional", "reasoning": false}, {"id": "sentiment_08", "template": "Examine the following tweet and identify its sentiment polarity. Choose one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "analytical", "reasoning": false}, {"id": "sentiment_09", "template": "Based on the content and tone of this tweet, what is its sentiment? Select one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "contextual", "reasoning": false}, {"id": "sentiment_10", "template": "Analyze the sentiment expressed in the tweet below. Categorize with exactly one word: Positive, Negative, or Neutral.\n\nTweet: {tweet}", "style": "comprehensive", "reasoning": false}]}, "explorative_coding": {"task_name": "explorative_coding", "description": "Commit message module classification task", "output_format": "Module: [module name (one word)]", "template_variables": ["repo_name", "message"], "expected_output": "single_word_module", "prompts": [{"id": "coding_01", "template": "Analyze the commit message from {repo_name} below and determine the relevant module. You can choose to think through the analysis if needed.\n\nCommitMessage: {message}\n\nPlease analyze the commit and identify the most relevant module. Consider the files mentioned, the functionality being changed, and the overall scope of the changes. Then provide your answer.\n\nModule: [module name (one word)]", "style": "analytical", "reasoning": true}, {"id": "coding_02", "template": "Based on this {repo_name} commit message, identify the primary module affected. Think about the code areas and functionality involved.\n\nCommitMessage: {message}\n\nAnalyze the commit and determine which module is most relevant. Consider file paths, function names, and the nature of changes.\n\nModule: [module name (one word)]", "style": "systematic", "reasoning": true}, {"id": "coding_03", "template": "Examine this commit from {repo_name} and classify it by module. Focus on the main area of the codebase being modified.\n\nCommitMessage: {message}\n\nWhat module does this commit primarily relate to? Consider the scope and impact of the changes.\n\nModule: [module name (one word)]", "style": "focused", "reasoning": true}, {"id": "coding_04", "template": "Review the following {repo_name} commit message and determine the most appropriate module classification.\n\nCommitMessage: {message}\n\nAnalyze the commit content and identify the primary module. Think about the functionality and code areas involved.\n\nModule: [module name (one word)]", "style": "comprehensive", "reasoning": true}, {"id": "coding_05", "template": "For this {repo_name} commit, identify which module it belongs to based on the changes described.\n\nCommitMessage: {message}\n\nConsider the files, functions, and overall scope to determine the most relevant module.\n\nModule: [module name (one word)]", "style": "direct", "reasoning": true}, {"id": "coding_06", "template": "Classify this {repo_name} commit by its primary module. Look at the technical details and scope of changes.\n\nCommitMessage: {message}\n\nWhat module is most relevant to this commit? Analyze the content and provide your classification.\n\nModule: [module name (one word)]", "style": "technical", "reasoning": true}, {"id": "coding_07", "template": "Determine the module for this {repo_name} commit based on the functionality and files involved.\n\nCommitMessage: {message}\n\nWhich module does this commit primarily affect? Consider the nature of the changes.\n\nModule: [module name (one word)]", "style": "functional", "reasoning": true}, {"id": "coding_08", "template": "Analyze this commit from {repo_name} and assign it to the most appropriate module category.\n\nCommitMessage: {message}\n\nBased on the commit details, what module should this be classified under?\n\nModule: [module name (one word)]", "style": "categorical", "reasoning": true}, {"id": "coding_09", "template": "Examine the {repo_name} commit below and identify its primary module based on the code changes.\n\nCommitMessage: {message}\n\nWhat module is this commit most closely associated with? Provide your analysis.\n\nModule: [module name (one word)]", "style": "associative", "reasoning": true}, {"id": "coding_10", "template": "Review this {repo_name} commit and determine which module it should be categorized under.\n\nCommitMessage: {message}\n\nConsider all aspects of the commit to identify the most relevant module.\n\nModule: [module name (one word)]", "style": "holistic", "reasoning": true}]}, "topic_labeling": {"task_name": "topic_labeling", "description": "Topic model keyword labeling task", "output_format": "Single concise label (≤ 6 words)", "template_variables": ["key_terms"], "expected_output": "short_phrase_label", "max_label_words": 6, "prompts": [{"id": "topic_01", "template": "You are an expert in labeling outputs from topic models. You will be given a list of words, and you need to label them with a single word or a simple phrase. You should not use more than 6 words to label them. Only respond with a single best label.\n\n{key_terms}", "style": "expert", "reasoning": false}, {"id": "topic_02", "template": "You specialize in naming topics based on term lists. For each list, return one concise label (≤ 6 words) that captures its meaning. Output only the single best-fitting label.\n\n{key_terms}", "style": "specialist", "reasoning": false}, {"id": "topic_03", "template": "Your task is to create short labels for topic model word lists. Each label must be no more than six words and should reflect the most accurate description. Provide only one label.\n\n{key_terms}", "style": "task_oriented", "reasoning": false}, {"id": "topic_04", "template": "Given a list of topic model terms, produce one short, accurate label in six words or fewer. Provide only that label, nothing else.\n\n{key_terms}", "style": "direct", "reasoning": false}, {"id": "topic_05", "template": "You are skilled at assigning concise labels to topic model outputs. Return only the single best label for each list, using no more than six words.\n\n{key_terms}", "style": "skilled", "reasoning": false}, {"id": "topic_06", "template": "You will receive a set of words from a topic model. Your job is to name the topic using one phrase or word, maximum six words. Output only that phrase.\n\n{key_terms}", "style": "naming", "reasoning": false}, {"id": "topic_07", "template": "As a topic labeling expert, name each word list from a topic model using one clear label (six words or fewer). Respond with just that label.\n\n{key_terms}", "style": "expert_clear", "reasoning": false}, {"id": "topic_08", "template": "Your role is to label topics. Given a list of related words, choose one short label (≤ 6 words) that best summarizes them. Provide nothing else.\n\n{key_terms}", "style": "summarizing", "reasoning": false}, {"id": "topic_09", "template": "Given terms from a topic model, assign one succinct label (up to six words) that captures the central theme. Respond with only that label.\n\n{key_terms}", "style": "thematic", "reasoning": false}, {"id": "topic_10", "template": "You are to summarize topic model outputs. For each list of terms, return a single, concise label no longer than six words. Give only that label.\n\n{key_terms}", "style": "concise", "reasoning": false}, {"id": "topic_11", "template": "Your task is topic naming. From each provided word list, produce one short phrase (six words or fewer) that best represents the topic. Output only that phrase.\n\n{key_terms}", "style": "representative", "reasoning": false}]}, "counterfactual_qa": {"task_name": "counterfactual_qa", "description": "Historical counterfactual question answering task", "output_format": "Detailed historical analysis and reasoning", "template_variables": ["question"], "expected_output": "comprehensive_analysis", "prompts": [{"id": "counterfactual_01", "template": "You are an expert in history, please answer the following question:\n\n{question}", "style": "expert_direct", "reasoning": true}, {"id": "counterfactual_02", "template": "As a professional historian, analyze this counterfactual scenario with careful consideration of historical context and causality:\n\n{question}\n\nProvide a detailed analysis considering multiple factors and potential outcomes.", "style": "professional_analytical", "reasoning": true}, {"id": "counterfactual_03", "template": "Consider this historical counterfactual question from multiple perspectives. Examine the political, economic, social, and cultural factors that would have been involved:\n\n{question}\n\nStructure your response with clear reasoning and evidence-based speculation.", "style": "multi_perspective", "reasoning": true}, {"id": "counterfactual_04", "template": "Analyze this counterfactual historical scenario by first identifying the key assumptions, then exploring the most likely alternative outcomes:\n\n{question}\n\nConsider both immediate and long-term consequences in your analysis.", "style": "systematic_analysis", "reasoning": true}, {"id": "counterfactual_05", "template": "As a historian specializing in counterfactual analysis, examine this scenario by considering what we know about the historical period, the key actors involved, and the broader historical forces at play:\n\n{question}\n\nProvide a nuanced analysis that acknowledges both possibilities and limitations of alternative outcomes.", "style": "specialist_nuanced", "reasoning": true}, {"id": "counterfactual_06", "template": "Examine this historical what-if scenario through the lens of cause and effect. What chain of events would have been set in motion?\n\n{question}\n\nTrace the potential ripple effects across different domains: political, social, economic, and cultural.", "style": "causal_chain", "reasoning": true}, {"id": "counterfactual_07", "template": "As a historical analyst, evaluate this counterfactual by comparing it to similar historical precedents and patterns:\n\n{question}\n\nDraw on analogous situations from history to assess the plausibility and likely outcomes of this alternative scenario.", "style": "comparative_historical", "reasoning": true}, {"id": "counterfactual_08", "template": "Approach this counterfactual question by first establishing the historical context, then methodically exploring how the proposed change would have altered the trajectory of events:\n\n{question}\n\nConsider the constraints and opportunities that would have shaped the alternative timeline.", "style": "methodical_contextual", "reasoning": true}, {"id": "counterfactual_09", "template": "Think like a historian examining primary sources and evidence. How would this alternative scenario have unfolded based on what we know about the people, institutions, and forces of the time?\n\n{question}\n\nGround your analysis in historical evidence while acknowledging the speculative nature of the exercise.", "style": "evidence_based", "reasoning": true}, {"id": "counterfactual_10", "template": "Consider this counterfactual scenario from the perspective of different historical actors - rulers, common people, institutions, and neighboring powers. How would each have responded?\n\n{question}\n\nAnalyze the scenario by examining multiple stakeholder perspectives and their likely interactions.", "style": "multi_stakeholder", "reasoning": true}]}}}