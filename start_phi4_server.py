#!/usr/bin/env python3
"""
启动Phi-4模型作为OpenAI兼容的API服务器
在8000端口监听，使用GPU 1
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def find_phi4_model():
    """查找Phi-4模型文件"""
    possible_names = [
        "phi-4-Q4_K_M.gguf",
        "phi4-Q4_K_M.gguf", 
        "phi-4.gguf",
        "phi4.gguf"
    ]
    
    possible_dirs = [
        Path("."),
        Path("models"),
        Path("./models"),
        Path.home() / "models",
        Path("/models"),
        Path("/data/models"),
    ]
    
    for directory in possible_dirs:
        if directory.exists():
            for name in possible_names:
                model_path = directory / name
                if model_path.exists():
                    return str(model_path)
    
    return None

def main():
    parser = argparse.ArgumentParser(description="Start Phi-4 as OpenAI-compatible API server")
    parser.add_argument("--model", "-m", help="Path to Phi-4 GGUF model file")
    parser.add_argument("--port", "-p", type=int, default=8000, help="Port to listen on (default: 8000)")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to (default: 0.0.0.0)")
    parser.add_argument("--gpu-layers", type=int, default=32, help="Number of GPU layers (default: 32)")
    parser.add_argument("--ctx-size", type=int, default=4096, help="Context size (default: 4096)")
    parser.add_argument("--threads", type=int, default=8, help="Number of threads (default: 8)")
    
    args = parser.parse_args()
    
    # 设置使用GPU 1
    os.environ['CUDA_VISIBLE_DEVICES'] = '1'
    print(f"🎮 Set CUDA_VISIBLE_DEVICES=1")
    
    # 查找模型文件
    model_path = args.model
    if not model_path:
        print("🔍 Searching for Phi-4 model...")
        model_path = find_phi4_model()
        if not model_path:
            print("❌ Phi-4 model not found!")
            print("Please specify the model path with --model or place the model in:")
            print("  - Current directory")
            print("  - ./models/")
            print("  - ~/models/")
            return 1
    
    # 检查模型文件是否存在
    if not Path(model_path).exists():
        print(f"❌ Model file not found: {model_path}")
        return 1
    
    print(f"📁 Using model: {model_path}")
    print(f"🌐 Starting server on {args.host}:{args.port}")
    print(f"🎮 GPU layers: {args.gpu_layers}")
    print(f"📏 Context size: {args.ctx_size}")
    
    # 构建llama-cpp-python服务器命令
    cmd = [
        "python3", "-m", "llama_cpp.server",
        "--model", model_path,
        "--host", args.host,
        "--port", str(args.port),
        "--n_gpu_layers", str(args.gpu_layers),
        "--n_ctx", str(args.ctx_size),
        "--n_threads", str(args.threads),
        "--chat_format", "chatml",  # 使用ChatML格式，兼容OpenAI
    ]
    
    print(f"🚀 Starting command: {' '.join(cmd)}")
    print("=" * 60)
    print("Server will be available at:")
    print(f"  http://{args.host}:{args.port}")
    print(f"  OpenAI-compatible endpoint: http://{args.host}:{args.port}/v1/chat/completions")
    print("=" * 60)
    print("Press Ctrl+C to stop the server")
    print()
    
    try:
        # 启动服务器
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed to start: {e}")
        return 1
    except FileNotFoundError:
        print("❌ llama-cpp-python not found!")
        print("Please install it with: pip install llama-cpp-python")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
