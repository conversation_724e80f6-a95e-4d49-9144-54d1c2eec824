#!/bin/bash
# 安装运行 phi-4-Q4_K_M.gguf 模型所需的依赖

echo "Installing dependencies for Phi-4 local model runner..."

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
echo "Python version: $python_version"

# 安装基础依赖
echo "Installing basic dependencies..."
pip3 install --upgrade pip

# 安装 llama-cpp-python
echo "Installing llama-cpp-python..."

# 检查是否有CUDA支持
if command -v nvcc &> /dev/null; then
    echo "CUDA detected, installing with CUDA support..."
    echo "Setting up for GPU 1..."
    export CUDA_VISIBLE_DEVICES=1
    CMAKE_ARGS="-DLLAMA_CUBLAS=on" pip3 install llama-cpp-python --force-reinstall --no-cache-dir
else
    echo "No CUDA detected, installing CPU-only version..."
    pip3 install llama-cpp-python
fi

# 安装其他依赖
echo "Installing additional dependencies..."
pip3 install numpy pandas

echo "Installation completed!"
echo ""
echo "Usage examples:"
echo "1. Interactive mode:"
echo "   python3 phi4_local_runner.py --model /path/to/phi-4-Q4_K_M.gguf --interactive"
echo ""
echo "2. Sentiment analysis:"
echo "   python3 phi4_local_runner.py --model /path/to/phi-4-Q4_K_M.gguf --text 'I love this day!'"
echo ""
echo "3. Text generation:"
echo "   python3 phi4_local_runner.py --model /path/to/phi-4-Q4_K_M.gguf --prompt 'Write a story about'"
echo ""
echo "Note: Make sure your phi-4-Q4_K_M.gguf file is accessible and specify the correct path."
