#!/usr/bin/env python3
"""
直接从MongoDB response_collections中提取采样数据
生成正确的sampled_semeval.csv和sampled_commits.csv
"""

import pandas as pd
from pymongo import MongoClient
import random
from typing import Dict, List, Any
import csv


def extract_sentiment_data(sample_size: int = 500):
    """从response_collections提取sentiment_analysis数据"""
    print(f"🔍 从response_collections提取sentiment数据 (样本数: {sample_size})")
    
    try:
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询sentiment_analysis任务的数据
        sentiment_docs = list(collection.find({"task_name": "sentiment_analysis"}))
        print(f"📊 MongoDB中sentiment_analysis数据: {len(sentiment_docs)} 条记录")
        
        if not sentiment_docs:
            print("❌ MongoDB中没有找到sentiment_analysis数据")
            client.close()
            return False
        
        # 提取唯一的tweet数据
        unique_tweets = {}
        for doc in sentiment_docs:
            # 从task_id中提取tweet_index
            task_id = doc.get('task_id', '')
            if 'twitter_sentiment_' in task_id:
                parts = task_id.split('_')
                if len(parts) >= 4:
                    tweet_id = parts[3]  # 获取tweet ID
                    try:
                        tweet_index = int(tweet_id)
                        if tweet_index not in unique_tweets:
                            unique_tweets[tweet_index] = {
                                'id': tweet_index,
                                'text': doc.get('input_text', ''),
                                'label': doc.get('reference_answer', '')
                            }
                    except ValueError:
                        continue
        
        print(f"📊 提取到唯一tweet数量: {len(unique_tweets)}")
        
        # 随机采样
        tweet_list = list(unique_tweets.values())
        if len(tweet_list) > sample_size:
            # 使用相同的随机种子确保可重现
            random.seed(42)
            sampled_tweets = random.sample(tweet_list, sample_size)
        else:
            sampled_tweets = tweet_list
        
        # 保存到CSV
        output_file = 'data/sampled_semeval.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['id', 'text', 'label'])
            writer.writeheader()
            writer.writerows(sampled_tweets)
        
        print(f"✅ 成功生成 {len(sampled_tweets)} 条sentiment采样数据到 {output_file}")
        
        # 显示标签分布
        label_counts = {}
        for tweet in sampled_tweets:
            label = tweet['label']
            label_counts[label] = label_counts.get(label, 0) + 1
        print(f"   标签分布: {label_counts}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ sentiment数据提取失败: {e}")
        return False


def extract_commits_data(sample_size: int = 500):
    """从response_collections提取explorative_coding数据"""
    print(f"🔍 从response_collections提取commits数据 (样本数: {sample_size})")
    
    try:
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询explorative_coding任务的数据
        coding_docs = list(collection.find({"task_name": "explorative_coding"}))
        print(f"📊 MongoDB中explorative_coding数据: {len(coding_docs)} 条记录")
        
        if not coding_docs:
            print("❌ MongoDB中没有找到explorative_coding数据")
            client.close()
            return False
        
        # 提取唯一的commit数据
        unique_commits = {}
        for doc in coding_docs:
            # 从task_id中提取commit SHA
            task_id = doc.get('task_id', '')
            if 'pytorch_commits_' in task_id:
                # 格式: task_pytorch_commits_70fb673e51decdd8bf4e55244d910a8e5680d12f_prompt_7_sampled
                # 提取SHA部分
                start_idx = task_id.find('pytorch_commits_') + len('pytorch_commits_')
                end_idx = task_id.find('_prompt_')
                if end_idx > start_idx:
                    commit_sha = task_id[start_idx:end_idx]
                    if commit_sha and commit_sha not in unique_commits:
                        unique_commits[commit_sha] = {
                            'sha': commit_sha,
                            'message': doc.get('input_text', ''),
                            'date': '',  # 这些信息在response_collections中可能不完整
                            'author': ''
                        }
        
        print(f"📊 提取到唯一commit数量: {len(unique_commits)}")
        
        # 随机采样
        commit_list = list(unique_commits.values())
        if len(commit_list) > sample_size:
            # 使用相同的随机种子确保可重现
            random.seed(42)
            sampled_commits = random.sample(commit_list, sample_size)
        else:
            sampled_commits = commit_list
        
        # 保存到CSV
        output_file = 'data/sampled_commits.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['sha', 'message', 'date', 'author'])
            writer.writeheader()
            writer.writerows(sampled_commits)
        
        print(f"✅ 成功生成 {len(sampled_commits)} 条commits采样数据到 {output_file}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ commits数据提取失败: {e}")
        return False


def extract_counterfactual_data():
    """从response_collections提取counterfactual_qa数据"""
    print(f"🔍 从response_collections提取counterfactual数据")
    
    try:
        # 连接MongoDB
        client = MongoClient('localhost', 27017)
        db = client['LLM-UQ']
        collection = db['response_collections']
        
        # 查询counterfactual_qa任务的数据
        cf_docs = list(collection.find({"task_name": "counterfactual_qa"}))
        print(f"📊 MongoDB中counterfactual_qa数据: {len(cf_docs)} 条记录")
        
        if not cf_docs:
            print("❌ MongoDB中没有找到counterfactual_qa数据")
            client.close()
            return False
        
        # 提取唯一的问题数据
        unique_questions = {}
        for doc in cf_docs:
            row_index = doc.get('row_index')
            if row_index is not None and row_index not in unique_questions:
                unique_questions[row_index] = {
                    'Category': doc.get('category', ''),
                    'Prompt': doc.get('input_text', '')
                }
        
        print(f"📊 提取到唯一问题数量: {len(unique_questions)}")
        
        # 按row_index排序
        sorted_questions = []
        for row_index in sorted(unique_questions.keys()):
            sorted_questions.append(unique_questions[row_index])
        
        # 保存到CSV
        output_file = 'data/counterfactual_questions_from_collections.csv'
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=['Category', 'Prompt'])
            writer.writeheader()
            writer.writerows(sorted_questions)
        
        print(f"✅ 成功生成 {len(sorted_questions)} 条counterfactual问题数据到 {output_file}")
        
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ counterfactual数据提取失败: {e}")
        return False


def validate_extracted_data():
    """验证提取的数据"""
    print("🔍 验证提取的数据...")
    
    files_to_check = [
        'data/sampled_semeval.csv',
        'data/sampled_commits.csv',
        'data/counterfactual_questions_from_collections.csv'
    ]
    
    for file_path in files_to_check:
        try:
            df = pd.read_csv(file_path)
            print(f"📊 {file_path}: {len(df)} 条记录")
            print(f"   字段: {list(df.columns)}")
            if len(df) > 0:
                print(f"   示例: {df.iloc[0].to_dict()}")
            print()
        except Exception as e:
            print(f"❌ 无法读取 {file_path}: {e}")


def main():
    """主函数"""
    print("🔍 从MongoDB response_collections提取采样数据")
    print("=" * 60)
    
    # 提取sentiment数据
    sentiment_ok = extract_sentiment_data(500)
    
    print("\n" + "-" * 30)
    
    # 提取commits数据
    commits_ok = extract_commits_data(500)
    
    print("\n" + "-" * 30)
    
    # 提取counterfactual数据
    cf_ok = extract_counterfactual_data()
    
    print("\n" + "=" * 60)
    
    # 验证提取的数据
    validate_extracted_data()
    
    print("📋 提取结果总结:")
    print(f"   Sentiment数据: {'✅ 成功' if sentiment_ok else '❌ 失败'}")
    print(f"   Commits数据: {'✅ 成功' if commits_ok else '❌ 失败'}")
    print(f"   Counterfactual数据: {'✅ 成功' if cf_ok else '❌ 失败'}")
    
    if sentiment_ok and commits_ok and cf_ok:
        print("\n🎉 所有数据提取成功！")
    else:
        print("\n⚠️  部分数据提取失败")
    
    return 0 if (sentiment_ok and commits_ok and cf_ok) else 1


if __name__ == "__main__":
    exit(main())
