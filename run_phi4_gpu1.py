#!/usr/bin/env python3
"""
专门使用GPU 1运行Phi-4模型的启动脚本
"""

import os
import sys
import argparse
from pathlib import Path

# 设置使用GPU 1
os.environ['CUDA_VISIBLE_DEVICES'] = '1'
print(f"🔧 Set CUDA_VISIBLE_DEVICES=1")

# 导入模型运行器
try:
    from phi4_local_runner import Phi4LocalRunner
except ImportError:
    print("❌ Error: phi4_local_runner.py not found in current directory")
    sys.exit(1)

def find_phi4_model():
    """查找Phi-4模型文件"""
    possible_names = [
        "phi-4-Q4_K_M.gguf",
        "phi4-Q4_K_M.gguf", 
        "phi-4.gguf",
        "phi4.gguf"
    ]
    
    possible_dirs = [
        Path("."),
        Path("models"),
        Path("./models"),
        Path.home() / "models",
        Path("/models"),
        Path("/data/models"),
    ]
    
    print("🔍 Searching for Phi-4 model...")
    for directory in possible_dirs:
        if directory.exists():
            print(f"   Checking: {directory}")
            for name in possible_names:
                model_path = directory / name
                if model_path.exists():
                    print(f"✅ Found model: {model_path}")
                    return str(model_path)
    
    return None

def main():
    parser = argparse.ArgumentParser(description="Run Phi-4 model on GPU 1")
    parser.add_argument("--model", "-m", help="Path to Phi-4 GGUF model file")
    parser.add_argument("--interactive", "-i", action="store_true", 
                       help="Run in interactive mode")
    parser.add_argument("--text", "-t", help="Text for sentiment analysis")
    parser.add_argument("--prompt", "-p", help="Prompt for text generation")
    parser.add_argument("--batch-file", "-b", help="File with texts for batch processing")
    parser.add_argument("--gpu-layers", type=int, default=32,
                       help="Number of layers to run on GPU (default: 32)")
    parser.add_argument("--max-tokens", type=int, default=100,
                       help="Maximum tokens to generate (default: 100)")
    parser.add_argument("--temperature", type=float, default=0.7,
                       help="Temperature for generation (default: 0.7)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # 查找模型文件
    model_path = args.model
    if not model_path:
        model_path = find_phi4_model()
        if not model_path:
            print("❌ Phi-4 model not found!")
            print("Please specify the model path with --model or place the model in:")
            print("  - Current directory")
            print("  - ./models/")
            print("  - ~/models/")
            return 1
    
    # 检查模型文件是否存在
    if not Path(model_path).exists():
        print(f"❌ Model file not found: {model_path}")
        return 1
    
    print(f"📁 Using model: {model_path}")
    print(f"🎮 GPU layers: {args.gpu_layers}")
    print(f"🌡️  Temperature: {args.temperature}")
    
    try:
        # 初始化模型
        print("🔄 Loading Phi-4 model on GPU 1...")
        runner = Phi4LocalRunner(
            model_path=model_path,
            n_gpu_layers=args.gpu_layers,
            gpu_id=1,  # 强制使用GPU 1
            verbose=args.verbose
        )
        print("✅ Model loaded successfully!")
        
        if args.interactive:
            print("\n🎯 Starting interactive mode...")
            runner.interactive_mode()
            
        elif args.text:
            print(f"\n📝 Analyzing sentiment: {args.text}")
            result = runner.sentiment_analysis(args.text)
            print(f"   Sentiment: {result['sentiment']}")
            print(f"   Confidence: {result['confidence']:.4f}")
            print(f"   Raw Response: {result['raw_response']}")
            print(f"   Time: {result['generation_time']:.2f}s")
            
        elif args.prompt:
            print(f"\n✍️  Generating text for: {args.prompt}")
            result = runner.generate_text(
                prompt=args.prompt,
                max_tokens=args.max_tokens,
                temperature=args.temperature
            )
            print(f"Generated: {result['text']}")
            print(f"Time: {result['generation_time']:.2f}s")
            
        elif args.batch_file:
            print(f"\n📊 Processing batch file: {args.batch_file}")
            try:
                with open(args.batch_file, 'r', encoding='utf-8') as f:
                    texts = [line.strip() for line in f if line.strip()]
                
                print(f"Found {len(texts)} texts to process")
                results = runner.batch_sentiment_analysis(texts)
                
                # 保存结果
                output_file = f"batch_results_{Path(args.batch_file).stem}.json"
                import json
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=2, ensure_ascii=False)
                
                print(f"✅ Results saved to: {output_file}")
                
                # 显示统计
                successful = len([r for r in results if r.get('sentiment')])
                print(f"📊 Successfully processed: {successful}/{len(texts)}")
                
            except FileNotFoundError:
                print(f"❌ Batch file not found: {args.batch_file}")
                return 1
                
        else:
            print("\n🤖 Phi-4 model ready!")
            print("Usage examples:")
            print(f"  Sentiment: python3 {sys.argv[0]} --text 'I love this day!'")
            print(f"  Generate:  python3 {sys.argv[0]} --prompt 'Write a story about'")
            print(f"  Interactive: python3 {sys.argv[0]} --interactive")
            
            # 简单测试
            test_text = "I love this beautiful day!"
            print(f"\n🧪 Quick test with: {test_text}")
            result = runner.sentiment_analysis(test_text)
            print(f"   Result: {result['sentiment']} (confidence: {result['confidence']:.4f})")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
