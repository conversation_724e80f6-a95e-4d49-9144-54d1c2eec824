#!/usr/bin/env python3
"""
测试Phi-4 API服务器是否正常工作
"""

import requests
import json
import time

def test_api(base_url="http://localhost:8000"):
    """测试API服务器"""
    
    print(f"🧪 Testing Phi-4 API at {base_url}")
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
        else:
            print(f"⚠️  Health check returned: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    # 测试OpenAI兼容的chat completions
    chat_url = f"{base_url}/v1/chat/completions"
    
    test_cases = [
        {
            "name": "Simple greeting",
            "messages": [{"role": "user", "content": "Hello!"}]
        },
        {
            "name": "Sentiment analysis",
            "messages": [{"role": "user", "content": "Classify the sentiment: I love this day! Answer with one word: Positive, Negative, or Neutral."}]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📝 Testing: {test_case['name']}")
        
        payload = {
            "model": "phi-4",
            "messages": test_case["messages"],
            "max_tokens": 50,
            "temperature": 0.7
        }
        
        try:
            start_time = time.time()
            response = requests.post(chat_url, json=payload, timeout=30)
            end_time = time.time()
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                print(f"✅ Response: {content}")
                print(f"⏱️  Time: {end_time - start_time:.2f}s")
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request error: {e}")
    
    return True

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Phi-4 API server")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--wait", action="store_true",
                       help="Wait for server to be ready")
    
    args = parser.parse_args()
    
    if args.wait:
        print("⏳ Waiting for server to be ready...")
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                response = requests.get(f"{args.url}/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Server is ready!")
                    break
            except:
                pass
            
            print(f"   Attempt {attempt + 1}/{max_attempts}...")
            time.sleep(2)
        else:
            print("❌ Server did not become ready in time")
            return 1
    
    success = test_api(args.url)
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
