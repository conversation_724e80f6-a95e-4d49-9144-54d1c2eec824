#!/bin/bash
# Phi-4 模型快速启动脚本

echo "🚀 Phi-4 Local Model Quick Start"
echo "================================="

# 检查是否已安装依赖
if ! python3 -c "import llama_cpp" 2>/dev/null; then
    echo "📦 Installing dependencies..."
    ./install_phi4_dependencies.sh
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
else
    echo "✅ Dependencies already installed"
fi

# 设置GPU 1
export CUDA_VISIBLE_DEVICES=1
echo "🎮 Set to use GPU 1"

# 检查GPU状态
if command -v nvidia-smi &> /dev/null; then
    echo "📊 GPU Status:"
    nvidia-smi --query-gpu=index,name,memory.used,memory.total --format=csv,noheader,nounits | grep "^1,"
else
    echo "⚠️  nvidia-smi not found, cannot check GPU status"
fi

echo ""
echo "🔍 Looking for Phi-4 model..."

# 查找模型文件
MODEL_PATH=""
POSSIBLE_PATHS=(
    "./phi-4-Q4_K_M.gguf"
    "./models/phi-4-Q4_K_M.gguf"
    "~/models/phi-4-Q4_K_M.gguf"
    "./phi4-Q4_K_M.gguf"
    "./models/phi4-Q4_K_M.gguf"
)

for path in "${POSSIBLE_PATHS[@]}"; do
    expanded_path=$(eval echo $path)
    if [ -f "$expanded_path" ]; then
        MODEL_PATH="$expanded_path"
        echo "✅ Found model: $MODEL_PATH"
        break
    fi
done

if [ -z "$MODEL_PATH" ]; then
    echo "❌ Phi-4 model not found!"
    echo "Please place your phi-4-Q4_K_M.gguf file in one of these locations:"
    for path in "${POSSIBLE_PATHS[@]}"; do
        echo "  - $path"
    done
    echo ""
    read -p "Enter the full path to your model file: " MODEL_PATH
    if [ ! -f "$MODEL_PATH" ]; then
        echo "❌ File not found: $MODEL_PATH"
        exit 1
    fi
fi

echo ""
echo "🎯 Choose an option:"
echo "1. Interactive mode"
echo "2. Quick sentiment test"
echo "3. Quick generation test"
echo "4. Custom command"
echo ""
read -p "Enter your choice (1-4): " choice

case $choice in
    1)
        echo "🎮 Starting interactive mode..."
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" --interactive
        ;;
    2)
        echo "😊 Testing sentiment analysis..."
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" --text "I love this beautiful sunny day!"
        echo ""
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" --text "This is the worst movie ever."
        echo ""
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" --text "The weather is okay today."
        ;;
    3)
        echo "✍️  Testing text generation..."
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" --prompt "Once upon a time" --max-tokens 50
        ;;
    4)
        echo "Available options:"
        echo "  --text 'your text'           - Sentiment analysis"
        echo "  --prompt 'your prompt'       - Text generation"
        echo "  --interactive                - Interactive mode"
        echo "  --batch-file filename        - Batch processing"
        echo "  --gpu-layers N               - GPU layers (default: 32)"
        echo "  --max-tokens N               - Max tokens (default: 100)"
        echo "  --temperature N              - Temperature (default: 0.7)"
        echo "  --verbose                    - Verbose output"
        echo ""
        read -p "Enter your command (without python3 run_phi4_gpu1.py): " custom_args
        python3 run_phi4_gpu1.py --model "$MODEL_PATH" $custom_args
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

echo ""
echo "✅ Done!"
