#!/usr/bin/env python3
"""
本地运行 phi-4-Q4_K_M.gguf 模型的代码
使用 llama-cpp-python 加载和运行量化的GGUF模型
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from llama_cpp import Llama
    logger.info("llama-cpp-python imported successfully")
except ImportError:
    logger.error("llama-cpp-python not found. Please install it with: pip install llama-cpp-python")
    sys.exit(1)

class Phi4LocalRunner:
    """本地Phi-4模型运行器"""
    
    def __init__(self,
                 model_path: str = "phi-4-Q4_K_M.gguf",
                 n_ctx: int = 4096,
                 n_threads: int = None,
                 n_gpu_layers: int = 32,  # 默认使用GPU加速
                 gpu_id: int = 1,  # 默认使用GPU 1
                 verbose: bool = False):
        """
        初始化Phi-4模型

        Args:
            model_path: GGUF模型文件路径
            n_ctx: 上下文长度
            n_threads: CPU线程数（None为自动）
            n_gpu_layers: GPU层数（0为纯CPU，32为推荐值）
            gpu_id: 使用的GPU编号（默认为1）
            verbose: 是否显示详细日志
        """
        self.model_path = Path(model_path)
        self.n_ctx = n_ctx
        self.n_threads = n_threads
        self.n_gpu_layers = n_gpu_layers
        self.gpu_id = gpu_id
        self.verbose = verbose
        self.llm = None

        # 设置使用指定的GPU
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        logger.info(f"Set CUDA_VISIBLE_DEVICES to GPU {gpu_id}")
        
        # 检查模型文件是否存在
        if not self.model_path.exists():
            logger.error(f"Model file not found: {self.model_path}")
            # 尝试在常见位置查找
            possible_paths = [
                Path("models") / model_path,
                Path("./") / model_path,
                Path.home() / "models" / model_path,
            ]
            
            for path in possible_paths:
                if path.exists():
                    logger.info(f"Found model at: {path}")
                    self.model_path = path
                    break
            else:
                raise FileNotFoundError(f"Model file not found: {model_path}")
        
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        logger.info(f"Loading model from: {self.model_path}")
        logger.info(f"Model size: {self.model_path.stat().st_size / (1024**3):.2f} GB")
        
        try:
            start_time = time.time()
            self.llm = Llama(
                model_path=str(self.model_path),
                n_ctx=self.n_ctx,
                n_threads=self.n_threads,
                n_gpu_layers=self.n_gpu_layers,
                verbose=self.verbose,
                logits_all=True,  # 启用logits输出用于概率计算
            )
            load_time = time.time() - start_time
            logger.info(f"Model loaded successfully in {load_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def generate_text(self, 
                     prompt: str,
                     max_tokens: int = 512,
                     temperature: float = 0.7,
                     top_p: float = 0.95,
                     stop: Optional[List[str]] = None,
                     echo: bool = False) -> Dict[str, Any]:
        """
        生成文本
        
        Args:
            prompt: 输入提示词
            max_tokens: 最大生成token数
            temperature: 温度参数
            top_p: top-p采样参数
            stop: 停止词列表
            echo: 是否在输出中包含输入
            
        Returns:
            Dict: 包含生成文本和元数据的字典
        """
        if not self.llm:
            raise RuntimeError("Model not loaded")
        
        logger.info(f"Generating text for prompt: {prompt[:50]}...")
        
        start_time = time.time()
        
        try:
            output = self.llm(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=top_p,
                stop=stop or [],
                echo=echo,
                logprobs=5,  # 返回top-5 logprobs
            )
            
            generation_time = time.time() - start_time
            
            result = {
                "text": output["choices"][0]["text"],
                "prompt": prompt,
                "finish_reason": output["choices"][0]["finish_reason"],
                "usage": output.get("usage", {}),
                "generation_time": generation_time,
                "logprobs": output["choices"][0].get("logprobs"),
                "model_path": str(self.model_path),
            }
            
            logger.info(f"Text generated in {generation_time:.2f} seconds")
            return result
            
        except Exception as e:
            logger.error(f"Error generating text: {e}")
            raise
    
    def sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """
        情感分析（单词输出）
        
        Args:
            text: 要分析的文本
            
        Returns:
            Dict: 情感分析结果
        """
        prompt = f"""Classify the sentiment of the following text. Respond with only one word: Positive, Negative, or Neutral.

Text: {text}

Sentiment:"""
        
        result = self.generate_text(
            prompt=prompt,
            max_tokens=10,
            temperature=0.1,  # 低温度确保一致性
            stop=["\n", ".", " "],
        )
        
        # 解析情感
        response_text = result["text"].strip()
        sentiment = None
        
        if "positive" in response_text.lower():
            sentiment = "Positive"
        elif "negative" in response_text.lower():
            sentiment = "Negative"
        elif "neutral" in response_text.lower():
            sentiment = "Neutral"
        else:
            # 如果没有明确匹配，取第一个词
            first_word = response_text.split()[0] if response_text.split() else ""
            sentiment = first_word.capitalize()
        
        return {
            "text": text,
            "sentiment": sentiment,
            "raw_response": response_text,
            "confidence": self._calculate_confidence(result.get("logprobs")),
            "generation_time": result["generation_time"],
            "logprobs": result.get("logprobs"),
        }
    
    def _calculate_confidence(self, logprobs_data: Optional[Dict]) -> float:
        """从logprobs计算置信度"""
        if not logprobs_data or not logprobs_data.get("tokens"):
            return 0.0
        
        # 取第一个生成token的概率作为置信度
        first_token_logprobs = logprobs_data.get("token_logprobs", [])
        if first_token_logprobs:
            import math
            return math.exp(first_token_logprobs[0])
        
        return 0.0
    
    def batch_sentiment_analysis(self, texts: List[str]) -> List[Dict[str, Any]]:
        """批量情感分析"""
        results = []
        
        logger.info(f"Starting batch sentiment analysis for {len(texts)} texts")
        
        for i, text in enumerate(texts):
            logger.info(f"Processing text {i+1}/{len(texts)}")
            try:
                result = self.sentiment_analysis(text)
                result["index"] = i
                results.append(result)
            except Exception as e:
                logger.error(f"Error processing text {i+1}: {e}")
                results.append({
                    "index": i,
                    "text": text,
                    "sentiment": None,
                    "error": str(e)
                })
        
        logger.info(f"Batch analysis completed. Processed {len(results)} texts")
        return results
    
    def interactive_mode(self):
        """交互模式"""
        print("Phi-4 Local Model Interactive Mode")
        print("Commands:")
        print("  /sentiment <text> - Analyze sentiment")
        print("  /generate <prompt> - Generate text")
        print("  /quit - Exit")
        print()
        
        while True:
            try:
                user_input = input(">>> ").strip()
                
                if user_input.lower() in ['/quit', '/exit', 'quit', 'exit']:
                    break
                elif user_input.startswith('/sentiment '):
                    text = user_input[11:]
                    result = self.sentiment_analysis(text)
                    print(f"Text: {text}")
                    print(f"Sentiment: {result['sentiment']}")
                    print(f"Confidence: {result['confidence']:.4f}")
                    print(f"Raw Response: {result['raw_response']}")
                    print()
                elif user_input.startswith('/generate '):
                    prompt = user_input[10:]
                    result = self.generate_text(prompt, max_tokens=100)
                    print(f"Generated: {result['text']}")
                    print()
                else:
                    # 默认为情感分析
                    if user_input:
                        result = self.sentiment_analysis(user_input)
                        print(f"Sentiment: {result['sentiment']} (confidence: {result['confidence']:.4f})")
                        print()
                        
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
        
        print("Goodbye!")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Phi-4 Local Model Runner")
    parser.add_argument("--model", "-m", default="phi-4-Q4_K_M.gguf", 
                       help="Path to GGUF model file")
    parser.add_argument("--interactive", "-i", action="store_true",
                       help="Run in interactive mode")
    parser.add_argument("--text", "-t", help="Text for sentiment analysis")
    parser.add_argument("--prompt", "-p", help="Prompt for text generation")
    parser.add_argument("--gpu-layers", type=int, default=32,
                       help="Number of layers to run on GPU (default: 32)")
    parser.add_argument("--gpu-id", type=int, default=1,
                       help="GPU ID to use (default: 1)")
    parser.add_argument("--threads", type=int, help="Number of CPU threads")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Enable verbose logging")
    
    args = parser.parse_args()
    
    try:
        # 初始化模型
        runner = Phi4LocalRunner(
            model_path=args.model,
            n_gpu_layers=args.gpu_layers,
            gpu_id=args.gpu_id,
            n_threads=args.threads,
            verbose=args.verbose
        )
        
        if args.interactive:
            runner.interactive_mode()
        elif args.text:
            result = runner.sentiment_analysis(args.text)
            print(json.dumps(result, indent=2, ensure_ascii=False))
        elif args.prompt:
            result = runner.generate_text(args.prompt)
            print(result["text"])
        else:
            print("No action specified. Use --interactive, --text, or --prompt")
            
    except Exception as e:
        logger.error(f"Error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
